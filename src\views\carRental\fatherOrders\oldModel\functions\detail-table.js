/**
 * @description:替换车信息
 * @param {type}
 * @return:
 */
export function replaceCarInfo() {
  return [
    {
      label: '替换车订单',
      prop: 'orderCode',
      minWidth: 200,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.orderCode,
          data: row,
          orderType: 11,
        })
      },
    },
    { label: '替换车订单状态', prop: 'orderStatusName', minWidth: 150 },
    { label: '替换车牌号', prop: 'carNo', minWidth: 170 },
    { label: '替换车架号', prop: 'vinCode', minWidth: 170 },
    {
      label: '是否转租赁',
      prop: 'isSubLease',
      minWidth: 100,
      render: (h, scope) => {
        return <span>{scope.row.isSubLease === 1 ? '是' : '否'}</span>
      },
    },
    {
      label: '转租赁父订单',
      prop: 'subletParentOrderCode',
      minWidth: 200,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.subletParentOrderCode,
          data: row,
          orderType: 0,
          primaryKey: 'subletParentOrderCode',
        })
      },
    },
    { label: '替换时间', prop: 'replaceTime', minWidth: 200 },
    { label: '换回时间', prop: 'backTime', minWidth: 200 },
    { label: '转租赁时间', prop: 'leaseTime', minWidth: 200 },
  ]
}

/**
 * @description:车辆资源列表（发起交车弹框逻辑系列）
 * @param {type}
 * @return:
 */
export function vehicleResourcesTableConfig() {
  return [
    { label: '订单商品', prop: 'ordersGood', minWidth: 200 },
    { label: '销售规格', prop: 'salesSpecifice', minWidth: 220 },
    { label: '车牌号码', prop: 'carNo', minWidth: 150 },
    { label: '车架号', prop: 'carVin', minWidth: 180 },
    { label: '所在场站', prop: 'station', minWidth: 180 },
    { label: '运营城市', prop: 'operateCity', minWidth: 120 },
    { label: '车辆状态', prop: 'status', minWidth: 120 },
  ]
}

/**
 * @description:换车信息列表配置
 * @param {type}
 * @return:
 */
export function transferInfo() {
  return [
    {
      label: '关联原车父订单',
      prop: 'originalParentCode',
      minWidth: 200,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.originalParentCode,
          data: row,
          orderType: 0,
          primaryKey: 'originalParentCode',
        })
      },
    },
    { label: '原车车牌号', prop: 'originalCarNo', minWidth: 150 },
    { label: '原车车架号', prop: 'originalVinCode', minWidth: 170 },
    { label: '预计换车时间', prop: 'expDeliveryTime', minWidth: 200 },
    { label: '换车审批时间', prop: 'endTime', minWidth: 200 },
    {
      label: '发起人',
      prop: 'initiatorName',
      minWidth: 170,
      formatter: ({ row }) => {
        if (row.initiatorName && row.initiator) {
          return `${row.initiatorName}（${row.initiator}）`
        } else if (row.initiatorName) {
          return row.initiatorName
        } else if (row.initiator) {
          return row.initiator
        } else return '--'
      },
    },
    { label: '发起时间', prop: 'createTime', minWidth: 200 },
  ]
}

/**
 * @description:转租赁信息列表配置
 * @param {type}
 * @return:
 */
export function transferLeaseCarInfo() {
  return [
    {
      label: '关联原车父订单',
      prop: 'originalParentCode',
      minWidth: 200,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.originalParentCode,
          data: row,
          orderType: 0,
          primaryKey: 'originalParentCode',
        })
      },
    },
    { label: '原车车牌号', prop: 'originalCarNo', minWidth: 150 },
    { label: '原车车架号', prop: 'originalVinNo', minWidth: 170 },
    { label: '转租赁时间', prop: 'leaseTime', minWidth: 200 },
    {
      label: '发起人',
      prop: 'initiatorName',
      minWidth: 170,
      formatter: ({ row }) => {
        if (row.initiatorName && row.initiator) {
          return `${row.initiatorName}（${row.initiator}）`
        } else if (row.initiatorName) {
          return row.initiatorName
        } else if (row.initiator) {
          return row.initiator
        } else return '--'
      },
    },
    { label: '发起时间', prop: 'initiatorTime', minWidth: 200 },
  ]
}
