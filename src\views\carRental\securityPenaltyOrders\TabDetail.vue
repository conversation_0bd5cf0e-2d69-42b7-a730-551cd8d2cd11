<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  components: {
    NewCarRentalSecurityPenaltyOrderDetail: () => import('./detail.vue'),
    OldCarRentalSecurityPenaltyOrderDetail: () => import('./oldModel/detail')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: ''
    }
  },
  created() {
    this.init(
      'OldCarRentalSecurityPenaltyOrderDetail',
      'NewCarRentalSecurityPenaltyOrderDetail'
    )
  }
}
</script>

<style lang="scss" scoped></style>
