<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalMileageMarginOrders',
  components: {
    NewConsignmentRentalMileageMarginOrders: () => import('./index.vue'),
    OldConsignmentRentalMileageMarginOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: ''
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalMileageMarginOrders',
      'NewConsignmentRentalMileageMarginOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
