<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate
      v-if="type == 0 || type == 1"
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      @searchClick="searchClick"
    ></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="common_child_orders_list"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setChildsModuleTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
/* eslint-disable indent */
import mixin from '@/mixins/table.js'
import { setChildsModuleTableHeadArr } from './functions/table'
import { getParentOrderSubList } from '@/api/common/rental/parent'
import orderMixins from '@/views/common/common/mixins/orderLink'
import { getSimpleDictByType } from '@/api/base'
import dictionaries from '@/utils/common.js'
export default {
  name: 'ChildsModule',
  mixins: [mixin, orderMixins],
  props: {
    // 当前父订单code
    parentCode: {
      type: String,
      default: '',
      required: true,
    },
    // 当前父订单id
    parentId: {
      type: String,
      default: '',
      required: true,
    },
    // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
    type: {
      type: [Number, String],
      default: 0,
      required: true,
    },
  },
  data() {
    return {
      // loading加载
      isLock: false,
      // 子账单信息列表数据
      tableList: [],
      // 子订单总数
      paginationTotal: 0,
    }
  },
  computed: {
    // 子订单列表配置
    setChildsModuleTableHeadArr,
    // 订单业务类型对应的字典查询
    businessType() {
      return {
        0: dictionaries.rentalOrderType,
        1: dictionaries.consignmentRentalOrderType,
        4: dictionaries.happlyRentalOrderType,
      }
    },
    // 子订单列表查询配置
    setFiltrateData() {
      return [
        {
          type: 'select',
          prop: 'orderType',
          label: '订单类型',
          width: '350px',
          optionsApiConf: {
            // 下拉的option可以动态获取请求，这里是配置
            parmas: { type: this.businessType[this.type] },
            resDataPath: 'data',
            apiFun: getSimpleDictByType,
          },
        },
      ]
    },
  },
  methods: {
    /**
     * @description:获取子订单列表信息
     * @param {type}
     * @return:
     */
    async getTableData() {
      await this.$nextTick(async () => {
        if (this.parentCode) {
          this.isLock = true
          const data = this.getParams()
          await getParentOrderSubList(
            {
              parentOrderCode: this.parentCode,
              ...data,
            },
            this.type
          )
            .then((res) => {
              this.tableList = (res.data.list || []).map((item) => ({
                ...item,
                isAdjustName: item.isAdjust == 0 ? '' : '轧差记录',
              }))
              this.paginationTotal = res.data.totalNum || 0
            })
            .finally(() => {
              this.isLock = false
            })
        }
      })
    },
    /**
     * @description:来自父订单的刷新
     * @param {type}
     * @return:
     */
    refresh() {
      this.getTableData()
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      this.searchParams = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
      }
      return this.searchParams
    },
  },
}
</script>
