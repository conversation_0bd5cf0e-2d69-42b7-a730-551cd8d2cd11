<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <DstFiltrate
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      :form-value="searchValue"
      @searchClick="searchClick"
    ></DstFiltrate>
    <div class="dst-table-content">
      <div v-if="isSelect" class="dst-table-over-btn-box">
        <sys-button :btn-list="btnList" :limit="14"></sys-button>
      </div>
      <sys-table
        local-key="happlyRental_fatherOrders"
        row-key="orderCode"
        ref="sysTable"
        :is-check-box="isSelect"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :is-loading="isLock"
        @sizeChange="sizeChange"
        @pageChange="pageChange"
        @selection-change="handleSelectionChange"
      />
    </div>
    <hand-car
      v-if="handCarFlag"
      :visible.sync="handCarFlag"
      :list="selectList"
      :customer-info="customerInfo"
      :type="type"
      @refresh="refresh"
    />
    <return-car
      v-if="returnCarFlag"
      :visible.sync="returnCarFlag"
      :list="selectList"
      :type="type"
      :station-list="stationList"
      @refresh="refresh"
    />
    <confirm-collect
      v-if="confirmCollectFlag"
      :visible.sync="confirmCollectFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="refresh"
    />
    <invalid-dialog
      v-if="invaildFlag"
      :visible.sync="invaildFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="refresh"
    />
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { btnList } from '@/views/common/parentOrder/dialog/list/functions/data.js'
import { setTableHeadArr } from './functions/table'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { getParentOrderList } from '@/api/common/rental/parent'
import { changeVariableName } from '@/utils/business'
import mutltipOptions from '@/views/common/parentOrder/dialog/list/mixins/mutltipOptions.js'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import { isEmpty } from '@/utils'
import marketOrgMixins from '@/views/common/common/mixins/marketOrg.js'
export default {
  name: 'OldHapplyRentalFatherOrders',
  components: {
    HandCar: () => import('@/views/common/parentOrder/dialog/list/HandCar.vue'),
    ReturnCar: () =>
      import('@/views/common/parentOrder/dialog/list/RefundCar.vue'),
    ConfirmCollect: () =>
      import('@/views/common/parentOrder/dialog/list/ConfirmCollect.vue'),
    InvalidDialog: () =>
      import('@/views/common/parentOrder/dialog/list/InvalidDialog.vue'),
  },
  mixins: [mixin, mutltipOptions, marketOrgMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 父订单总数
      paginationTotal: 0,
      // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
      type: 4,
      // 当前选中的父订单数据
      selectList: [],
    }
  },
  computed: {
    // 父订单搜索配置项
    setFiltrateData,
    // 父订单列表表头项
    setTableHeadArr,
    // 父订单列表按钮配置
    btnList,
    // 判断是否存在多选操作项
    isSelect() {
      return this.btnList.some((item) => !!item.show)
    },
  },
  methods: {
    /**
     * @description:请求父订单列表数据
     * @param {type}
     * @return:
     */
    getTableData() {
      if (this.initFlag) {
        this.isLock = true
        const data = this.getParams()
        getParentOrderList(data, this.type)
          .then((res) => {
            this.paginationTotal = res.data.totalNum
            this.tableList = (res.data.list || []).map((item) => ({
              ...item,
              businessTypeName: isEmpty(item.originalParentCode)
                ? '欢乐租'
                : '更欢乐',
            }))
            this.initSourceStatus()
          })
          .finally(() => {
            this.isLock = false
          })
      }
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      this.searchParams = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
        orderType: 4, // 欢乐租特有数据类型
      }
      changeVariableName(this.searchParams, 'createTime', true, 'Time')
      changeVariableName(this.searchParams, 'realDeliveryTime', true, 'Time')
      changeVariableName(this.searchParams, 'realBackCarTime', true, 'Time')
      const expLeaseMonth = this.getMouthData(
        this.searchParams,
        'expLeaseMonth'
      )
      this.searchParams.expLeaseStartMonth = expLeaseMonth.start
      this.searchParams.expLeaseEndMonth = expLeaseMonth.end
      const realLeaseMonth = this.getMouthData(
        this.searchParams,
        'realLeaseMonth'
      )
      this.searchParams.realLeaseStartMonth = realLeaseMonth.start
      this.searchParams.realLeaseEndMonth = realLeaseMonth.end
      if (this.searchParams.regionIds && this.searchParams.regionIds.length) {
        this.searchParams.serviceOrgCityCode =
          this.searchParams.regionIds[this.searchParams.regionIds.length - 1]
      }
      const { parentOrderCodeBatch, carNoBatch, marketingOrgCodeItems } =
        this.searchParams
      parentOrderCodeBatch &&
        (this.searchParams.parentOrderCodes = parentOrderCodeBatch.split(','))
      carNoBatch && (this.searchParams.carNoList = carNoBatch.split(','))
      delete this.searchParams.regionIds
      if (!isEmpty(this.searchParams.marketingOrgCodeItems)) {
        this.searchParams.marketingOrgCodeList = [
          marketingOrgCodeItems[marketingOrgCodeItems.length - 1],
        ]
      }
      delete this.searchParams.marketingOrgCodeItems
      this.getCustomerIds(this.searchParams, true)
      return this.searchParams
    },
    /**
     * @description:拆分查询项月份数据
     * @param {object} data 当前的数据集合
     * @param {key} string 所对应的字段名称
     * @return:
     */
    getMouthData(data, key) {
      if (data[key]) {
        const arr = data[key].split('-')
        const pararms = {
          start: this.dealData(arr[0]),
          end: this.dealData(arr[1]),
        }
        delete data[key]
        return pararms
      } else {
        delete data[key]
        return {
          start: '',
          end: '',
        }
      }
    },
    /**
     * @description:将数据格式化处理
     * @param {string} data 当前需要格式化的字符串
     * @return:
     */
    dealData(data) {
      return data !== '0' ? (data ? Number(data) : data) : null
    },
    /**
     * @description:APP下单
     * @param {type}
     * @return:
     */
    orderAPP() {
      this.isLock = true
      getParentOrderList()
        .then(async (res) => {
          this.$message.success('下单成功')
          this.getTableData()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:跳转详情页面
     * @param {type}
     * @return:
     */
    goDetail(row) {
      this.$router.push({
        path: '/happlyRental/fatherOrders/detail',
        query: {
          id: row.id,
          name: 'BaseInfo',
          code: row.orderCode,
          type: this.type,
        },
      })
    },
    /**
     * @description:列表选中触发
     * @param {array} list 当前选中的列表数据
     * @return:
     */
    handleSelectionChange(list) {
      this.selectList = _.cloneDeep(list)
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/views/common/common/styles/table.scss';
</style>
