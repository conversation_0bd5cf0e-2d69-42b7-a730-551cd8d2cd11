<template>
  <dstForm
    ref="ruleForm"
    label-width="140px"
    mode-type="detail"
    v-model="baseInfoForm"
    :data-arr="giveConfig"
  >
    <template slot="releaseRecordId">
      <template
        v-if="
                baseInfoForm.benefitRecordId && !$isEmpty(baseInfoForm.benefitRecordId.split(',')) &&
                  baseInfoForm.benefitRecordId.split(',').some(subItem => !!subItem)
              "
      >
        <span v-for="(item, index) in baseInfoForm.benefitRecordId.split(',')" :key="index">
          <template v-if="item">
            <sys-link :underline="false" @click="benefitRecordItem(item)">{{ item }}</sys-link>
            <span v-if="index !== baseInfoForm.benefitRecordId.split(',').length - 1">、</span>
          </template>
        </span>
      </template>
      <span v-else>-</span>
    </template>
  </dstForm>
</template>

<script>
import { giveConfig } from '../functions/data'
export default {
  name: 'BenefitRecord',
  props: {
    // 赠送信息
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      reuqired: true,
    },
  },
  computed: {
    // 赠送信息表单配置
    giveConfig
  },
  methods: {
    /**
     * @description:跳转权益卡详情
     * @param {string} item 当前保权益卡数据
     */
    benefitRecordItem(item) {
      if (item) {
        this.$linkTo({
          model: 'cec',
          path: '/goodsCenter/equityCard/recordDetail',
          query: { id: item }
        })
      } else {
        this.$message.warning('缺少权益卡数据信息！')
      }
    }
  }
}
</script>

<style>
</style>
