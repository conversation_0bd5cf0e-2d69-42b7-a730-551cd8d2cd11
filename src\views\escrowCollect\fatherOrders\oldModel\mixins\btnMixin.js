import { getParentOrderSubList } from '@/api/common/rental/parent'
import { assignedOrderCreate } from '@/api/consignmentRental/fatherOrders'

export default {
  computed: {
    // 按钮数据
    btnList() {
      return [
        {
          show: true,
          click: this.cancelOrder,
          text: '取消订单',
        },
        // carStatus  车状态，1待支付，2待交车，3交车中，4已交车，5还车中，6已还车
        {
          show: this.baseInfoForm.carStatus === 2,
          click: this.handCar,
          text: '发起交车',
        },
        {
          show: this.baseInfoForm.carStatus === 3,
          click: this.invalidCar,
          text: '作废交车',
        },
        {
          show: this.baseInfoForm.carStatus === 4,
          click: this.returnCar,
          text: '发起还车',
        },
        {
          show: this.baseInfoForm.carStatus === 5,
          click: this.invalidReturnCar,
          text: '作废还车',
        },
        {
          show: true,
          click: this.backCar,
          text: '确定退车',
        },
        {
          show: true,
          click: this.initTransfer,
          text: '发起车辆过户',
        },
      ]
    },
  },
  methods: {
    /**
     * @description:取消订单
     * @param {type}
     * @return:
     */
    cancelOrder() {
      this.componentKey = 'CancelOrder'
    },
    /**
     * @description:发起交车
     * @param {type}
     * @return:
     */
    handCar() {
      this.isLock = true
      getParentOrderSubList({}, this.type)
        .then(async () => {
          this.isLock = false
          this.componentKey = 'HandCar'
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:作废交车
     * @param {type}
     * @return:
     */
    invalidCar() {
      this.componentKey = 'InvalidCar'
    },
    /**
     * @description:发起还车
     * @param {type}
     * @return:
     */
    returnCar() {
      this.isLock = true
      getParentOrderSubList({}, this.type)
        .then(async () => {
          this.isLock = false
          this.componentKey = 'ReturnCar'
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:作废还车
     * @param {type}
     * @return:
     */
    invalidReturnCar() {
      this.componentKey = 'InvalidReturnCar'
    },
    /**
     * @description:确定退车
     * @param {type}
     * @return:
     */
    backCar() {
      this.componentKey = 'BackCar'
    },
    /**
     * @description:发起车辆过户
     * @param {type}
     * @return:
     */
    initTransfer() {
      this.isLock = true
      assignedOrderCreate()
        .then(async (res) => {
          this.$message.success('发起过户成功')
          this.refresh()
        })
        .finally(() => {
          this.isLock = false
        })
    },
  },
}
