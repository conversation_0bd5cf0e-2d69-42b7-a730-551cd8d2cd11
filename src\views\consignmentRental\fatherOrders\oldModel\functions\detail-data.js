/* eslint-disable jsx-quotes */
import { showData } from '@/utils/business'

/**
 * @description:基础信息1
 * @param {type}
 * @return:
 */
export function baseInfo1(v, key) {
  return [
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'createTime',
      label: '创建时间',
      type: 'text',
    },
    {
      prop: 'payEndTime',
      label: '支付截止时间',
      type: 'text',
    },
    {
      prop: 'carUseName',
      label: '车辆用途',
      type: 'text',
    },
    {
      prop: 'isBondName',
      label: '保证金',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'performTypeName',
      label: '履约条件',
      type: 'text',
    },
    {
      prop: 'cancelTime',
      label: '取消时间',
      type: 'text',
    },
  ]
}

/**
 * @description:基本信息2
 * @param {type}
 * @return:
 */
export function baseInfo2(v, key) {
  return [
    {
      prop: 'orderSourceName',
      label: '下单渠道',
      type: 'text',
    },
    {
      prop: 'orderChannel',
      label: '订单渠道',
      type: 'text',
    },
    {
      prop: 'cityName',
      label: '城市',
      type: 'text',
    },
    {
      prop: 'subName',
      label: '集团归属',
      type: 'text',
    },
    {
      prop: 'regionName',
      label: '大区归属',
      type: 'text',
    },
    {
      prop: 'serviceOrgCityName',
      label: '服务组织',
      type: 'text',
    },
    {
      prop: 'marketingOrgName',
      label: '营销组织',
      type: 'text',
    },
    {
      prop: 'customerGroupTypeName',
      label: '业务通路',
      type: 'text',
    },
    {
      prop: 'carNo',
      label: '车牌号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'vinCode',
      label: '车架号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'expDeliveryTime',
      label: '预计交车时间',
      type: 'text',
    },
    // {
    //   prop: 'expBackCarTime',
    //   label: '预计履约结束日期',
    //   type: 'text',
    // },
    {
      prop: 'expireTime',
      label: '订单有效期',
      type: 'render',
      class: 'font-bold',
      render: (h, ctx) => {
        const text = showData({
          data: [v.baseInfoForm.expireStartTime, v.baseInfoForm.expireEndTime],
          showEmpty: true,
          spliter: '~',
          type: 'time',
          strict: false,
        })
        return text !== '-' ? (
          <el-tooltip effect="dark" content={text} placement="top">
            <div class="font-bold font-text">{text}</div>
          </el-tooltip>
        ) : (
          <span> - </span>
        )
      },
    },
    {
      prop: 'lossScenePaymentName',
      label: '定损支付方',
      type: 'text',
    },
    {
      prop: 'realStartTime',
      label: '起租日期',
      type: 'text',
    },
    {
      prop: 'realEndTime',
      label: '止租日期',
      type: 'text',
    },
    // {
    //   prop: 'performTime',
    //   label: '订单履约时间',
    //   type: 'render',
    //   class: 'font-bold',
    //   render: (h, ctx) => {
    //     const text = showData({
    //       data: [
    //         v.baseInfoForm.performStartTime,
    //         v.baseInfoForm.performEndTime,
    //       ],
    //       showEmpty: true,
    //       spliter: '~',
    //       type: 'time',
    //       strict: false,
    //     })
    //     return text !== '-' ? (
    //       <el-tooltip effect="dark" content={text} placement="top">
    //         <div class="font-bold font-text">{text}</div>
    //       </el-tooltip>
    //     ) : (
    //       <span> - </span>
    //     )
    //   },
    // },
    // {
    //   prop: 'expLeaseMonth',
    //   label: '预计租赁时长（月）',
    //   type: 'text',
    // },
    // {
    //   prop: 'realLeaseMonth',
    //   label: '实际租赁时长（月）',
    //   type: 'text',
    // },
    // {
    //   prop: 'expAmount',
    //   label: '预计收入',
    //   type: 'render',
    //   render: (h, item, parent, { form }) => {
    //     return (
    //       <span>
    //         {showData({ data: [v.baseInfoForm.expAmount], frontWord: '￥' })}
    //       </span>
    //     )
    //   },
    // },
    // {
    //   prop: 'realAmount',
    //   label: '已确认收入',
    //   type: 'render',
    //   render: (h, item, parent, { form }) => {
    //     return (
    //       <span>
    //         {showData({ data: [v.baseInfoForm.realAmount], frontWord: '￥' })}
    //       </span>
    //     )
    //   },
    // },
    {
      prop: 'orderRecommenderInfo',
      label: '推荐人',
      type: 'slot',
      class: 'dst-block order-recommender-info',
    },
    {
      prop: 'orderSalemansInfo',
      label: '业务归属',
      type: 'slot',
      class: 'dst-block order-saleman-info',
    },
  ]
}
