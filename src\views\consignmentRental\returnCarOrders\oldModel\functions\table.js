import { showData } from '@/utils/business'

/**
 * @description:车辆定损订单列表数据
 * @param {type}
 * @return:
 */
export function setTableHeadArr() {
  return [
    { label: '退车单编码', prop: 'backCarCode', minWidth: 180 },
    { label: '子订单编码', prop: 'orderCode', minWidth: 220 },
    {
      label: '父订单编码',
      prop: 'parentOrderCode',
      minWidth: 220,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.parentOrderCode,
          data: row,
          orderType: 0,
        })
      },
    },
    { label: '客户编码', prop: 'customerId', minWidth: 180 },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '合同编码', prop: 'contractCode', minWidth: 230 },
    { label: '合同乙方', prop: 'contractPartyName', minWidth: 220 },
    {
      label: '应付金额',
      prop: 'payFee',
      minWidth: 110,
      render(h, scope) {
        return (
          <span>{showData({ data: [scope.row.payFee], frontWord: '￥' })}</span>
        )
      },
    },
    { label: '车牌号', prop: 'carNo', minWidth: 100 },
    { label: '车架号', prop: 'vinCode', minWidth: 220 },
    { label: '订单状态', prop: 'orderStatusName', minWidth: 100 },
    { label: '创建时间', prop: 'createTime', minWidth: 180 },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '详情',
          click: ({ row }) =>
            this.goDetail({
              value: row.orderCode,
              data: row,
              orderType: row.orderType,
            }),
        },
      ],
    },
  ]
}
