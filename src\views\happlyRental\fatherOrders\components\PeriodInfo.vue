<template>
  <sys-card title="分期信息" slot-name="periodInfo">
    <dst-form
      ref="ruleForm"
      slot="periodInfo_body"
      label-width="160px"
      mode-type="detail"
      v-model="ruleForm"
      :data-arr="periodInfoConfig"
    >
    </dst-form>
  </sys-card>
</template>

<script>
/* eslint-disable jsx-quotes */
export default {
  name: 'PeriodInfo',
  props: {
    // 父订单基础信息
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  data() {
    return {
      // 分期信息数据
      ruleForm: {},
    }
  },
  computed: {
    // 分期信息展示项处理
    periodInfoConfig() {
      return [
        {
          prop: 'period',
          label: '商品总期数',
          type: 'text',
        },
        {
          prop: 'instalmentNum',
          label: '分期期数',
          type: 'text',
        },
        {
          prop: 'instalmentFactor',
          label: '分期系数',
          type: 'text',
        },
        {
          prop: 'instalmentGoodPrice',
          label: '商品成交价',
          type: 'text',
        },
        {
          prop: 'instalmentAfterPrice',
          renderLabel: () => {
            return (
              <div class="myself-box">
                <span>订单金额（分期后）</span>
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="分期后订单金额=商品成交价/分期期数*分期系数+商品成交价"
                  placement="top-start"
                >
                  <i class="el-icon-question" />
                </el-tooltip>
              </div>
            )
          },
          type: 'text',
        },
      ]
    },
  },
  watch: {
    baseInfoForm: {
      handler(newVal) {
        this.ruleForm = _.cloneDeep(newVal ? newVal.instalmentVO || {} : {})
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ .el-card__body {
  max-height: inherit !important;
  .el-icon-question {
    color: #0ac1b9;
  }
}
</style>
