<template>
  <div class="dst-add-card-wrap dstform--demo-wrap" v-loading="isLock">
    <div class="main-box">
      <!-- 基本信息 -->
      <base-info :base-info-form="baseInfoForm" :type="type"></base-info>
      <!-- 商品信息 -->
      <goods-info
        ref="goodsInfo"
        :type="type"
        :base-info-form="baseInfoForm"
        :code="orderCode"
      ></goods-info>
      <!-- 异常交车商品 -->
      <abnormal-delivery-goods-info
        ref="abnormalDeliveryGoodsInfo"
        :type="type"
        :base-info-form="baseInfoForm"
        :code="orderCode"
      />
      <!-- 费用信息 -->
      <fee-info
        ref="feeInfo"
        is-new
        :type="type"
        :base-info-form="baseInfoForm"
        :id="orderId"
        :code="orderCode"
        @refresh="refresh"
      ></fee-info>
      <!-- 履约信息 -->
      <agree-module
        :type="type"
        ref="agreeInfo"
        :base-info-form="baseInfoForm"
      ></agree-module>
      <!-- 客户信息 -->
      <customer-info :base-info-form="baseInfoForm"></customer-info>
      <!-- 结算信息 -->
      <bill-info
        ref="billInfo"
        is-new
        :type="type"
        :base-info-form="baseInfoForm"
      ></bill-info>
    </div>
  </div>
</template>

<script>
import { BusEmit } from '@/mixins/busEvent'
import BaseInfo from './components/BaseInfo'
import {
  fetchRentalOrderDetail,
  getCarRentalOrderCategory,
} from '@/utils/system/business'
import { isFunction } from '@/utils/types'
import rentalComponents from '@/views/common/childOrders/detailTableList'
const components = { BaseInfo, ...rentalComponents }
delete components.DeductInfo
export default {
  name: 'NewCarRentalConventLeaseOrderDetail',
  components,
  data() {
    return {
      // loading加载
      isLock: false,
      // 基本信息详情展示
      baseInfoForm: {},
    }
  },
  computed: {
    // 子订单id
    orderId() {
      return this.$route.query.id
    },
    // 子订单code
    orderCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return (this.orderCode && Number(this.orderCode.slice(0, 2))) || 0
    },
  },
  created() {
    this.getDetailData()
  },
  methods: {
    /**
     * @description:获取页面详情数据
     * @param {function} callback 请求回调
     */
    async getDetailData(callback) {
      this.isLock = true
      try {
        this.baseInfoForm = await fetchRentalOrderDetail(this.orderCode, {
          orderCategory: getCarRentalOrderCategory('conventLeaseOrders'),
          queryItems: [1, 2, 3, 4, 5, 8, 9],
        })
      } finally {
        isFunction(callback) && callback()
        this.isLock = false
      }
    },
    /**
     * @description:刷新页面数据
     * @param {string} type 区分费用模块和抵扣模块
     */
    refresh() {
      this.$nextTick(() => {
        this.getDetailData(() => {
          Object.values(components).forEach((element) => {
            BusEmit(element.name, 'refresh')
          })
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
