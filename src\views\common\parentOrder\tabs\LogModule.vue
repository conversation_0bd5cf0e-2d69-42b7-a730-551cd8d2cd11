<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate
      v-if="type == 0 || type == 1"
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      @searchClick="searchClick"
    ></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="common_roll_log_list"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="orderLogConfig"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { orderLogConfig } from './functions/table'
import { logList, getParentOrderType } from '@/api/common/rental/parent'
import orderMixins from '@/views/common/common/mixins/orderLink'
export default {
  name: 'LogModule',
  mixins: [mixin, orderMixins],
  props: {
    // 当前父订单id
    parentId: {
      type: [String, Number],
      default: '',
      required: true,
    },
    // 当前父订单code
    parentCode: {
      type: String,
      default: '',
      required: true,
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type: {
      type: [String, Number],
      default: 0,
      required: true,
    },
  },
  data() {
    return {
      // laoding加载
      isLock: false,
      // 订单日志信息列表数据
      tableList: [],
      // 订单日志总数
      paginationTotal: 0,
    }
  },
  computed: {
    // 订单日志列表配置
    orderLogConfig,
    // 子订单列表查询配置
    setFiltrateData() {
      return [
        {
          type: 'select',
          prop: 'businessNode',
          label: '事件类型',
          width: '350px',
          optionsApiConf: {
            resDataPath: 'data',
            apiFun: (data) => getParentOrderType(data, this.type),
          },
          props: {
            value: 'eventType',
            label: 'eventName',
          },
        },
      ]
    },
  },
  methods: {
    /**
     * @description:获取订单日志列表信息
     */
    getTableData() {
      this.$nextTick(async () => {
        if (this.parentCode) {
          this.isLock = true
          const data = this.getParams()
          await logList(data, this.type)
            .then((res) => {
              this.tableList = res.data.list || []
              this.paginationTotal = res.data.totalNum || 0
            })
            .finally(() => {
              this.isLock = false
            })
        }
      })
    },
    /**
     * @description:来自父订单的刷新
     */
    refresh() {
      this.getTableData()
    },
    /**
     * @description:格式化列表请求参数
     */
    getParams() {
      this.searchParams = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        parentDataCode: this.parentCode,
        ...this.searchData,
      }
      return this.searchParams
    },
  },
}
</script>
