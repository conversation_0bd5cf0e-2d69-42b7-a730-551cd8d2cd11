/* eslint-disable jsx-quotes */
import { showData } from '@/utils/business'

/**
 * @description:赠送信息
 * @param {type}
 * @return:
 */
export function giveInfo() {
  return [
    {
      label: '商品劵编码',
      prop: 'goodsCouponCode',
      minWidth: 180,
    },
    { label: '商品劵名称', prop: 'goodsCouponName', minWidth: 150 },
    {
      label: '有效期',
      prop: 'validPeriodTime',
      minWidth: 360,
      render(h, scope) {
        return (
          <span>
            {showData({
              data: [
                scope.row.validPeriodStartTime,
                scope.row.validPeriodEndTime,
              ],
              showEmpty: true,
              spliter: '~',
            })}
          </span>
        )
      },
    },
    // {
    //   label: '适用范围',
    //   prop: 'orderCode',
    //   minWidth: 220,
    //   type: 'link',
    //   click: ({ row }) => {
    //     this.goDetail({
    //       value: row.orderCode,
    //       data: row,
    //       orderType: row.orderType || row.orderCode.slice(3, 4),
    //     })
    //   },
    // },
    {
      label: '适用范围',
      prop: 'giveRangeName',
      minWidth: 220,
    },
    { label: '核销状态', prop: 'writeOffStatusName', minWidth: 100 },
    // { label: '核销城市', prop: 'writeOffCity', minWidth: 100 },
    // { label: '核销时间', prop: 'writeOffTime', minWidth: 180 },
  ]
}
