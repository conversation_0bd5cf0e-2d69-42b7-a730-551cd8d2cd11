<template>
  <div class="dst-add-card-wrap dstform--demo-wrap" v-loading="isLock">
    <div class="main-box">
      <!-- 动态组件集合 -->
      <template v-for="item in componentList">
        <component
          ref="componentItem"
          :type="type"
          :is="item"
          :key="item"
          :base-info-form="baseInfoForm"
          :id="orderId"
          :code="orderCode"
          @refresh="refresh"
        ></component>
      </template>
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { getViolationOrderBasic } from '@/api/carRental/violationOrders'
import { BusEmit } from '@/mixins/busEvent'
import BaseInfo from './components/BaseInfo'
import rentalComponents from '@/views/common/childOrders/detailTableList'
const { GoodsInfo, GiveInfo, FeeInfo, CustomerInfo, AgreeModule } =
  rentalComponents
const components = {
  BaseInfo,
  GoodsInfo,
  CustomerInfo,
  GiveInfo,
  FeeInfo,
  AgreeModule,
}
export default {
  name: 'OldCarRentalViolationOrderDetail',
  components,
  data() {
    return {
      // loading加载
      isLock: false,
      // 基本信息详情展示
      baseInfoForm: {},
      // 动态组件键值
      componentList: Object.values(components).map((item) => item.name),
    }
  },
  computed: {
    // 子订单id
    orderId() {
      return this.$route.query.id
    },
    // 子订单id
    orderCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return (this.orderCode && Number(this.orderCode.slice(0, 2))) || 0
    },
  },
  created() {
    this.getDetailData()
  },
  methods: {
    /**
     * @description:获取页面详情数据
     * @param {type}
     * @return:
     */
    getDetailData() {
      this.isLock = true
      getViolationOrderBasic({ orderCode: this.orderCode })
        .then((res) => {
          const baseInfoForm = res.data || {}
          if (
            baseInfoForm.payType &&
            [1, 2].includes(Number(baseInfoForm.payType))
          ) {
            baseInfoForm.payTypeName += '或自行办理'
          }
          this.baseInfoForm = _.cloneDeep(baseInfoForm)
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:刷新页面数据
     * @param {type}
     * @return:
     */
    refresh() {
      this.$nextTick(() => {
        this.getDetailData()
        Object.values(this.componentList).forEach((element) => {
          BusEmit(element, 'refresh')
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
