<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <DstFiltrate
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      @searchClick="searchClick"
    ></DstFiltrate>
    <div class="dst-table-content">
      <sys-table
        local-key="escrowCollect_fatherOrders"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :is-loading="isLock"
        @sizeChange="sizeChange"
        @pageChange="pageChange"
      />
    </div>
    <dst-dialog
      v-if="contractCarFlag"
      width="500px"
      title="合同提车"
      v-loading="isLock"
      v-drag
      @beforeClose="infoHandleClose"
      @beforeSure="saveInfo"
    >
      <dstForm
        ref="dataArrbase"
        :data-arr="dialogDataArr"
        v-model="ruleData"
        label-width="120px"
      ></dstForm>
    </dst-dialog>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData, dialogDataArr } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { getParentOrderList } from '@/api/common/rental/parent'
import { changeVariableName } from '@/utils/business'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import { isEmpty } from '@/utils'
// import { getMarketingList } from '@/api/common'
import marketOrgMixins from '@/views/common/common/mixins/marketOrg.js'
export default {
  name: 'OldEscrowCollectFatherOrders',
  mixins: [mixin, marketOrgMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 父订单总数
      paginationTotal: 0,
      // 合同提车弹框
      contractCarFlag: false,
      // 合同提车表单数据
      ruleData: {
        carNumber: '',
        cashDeposit: '',
        performanceCondition: '',
      },
      // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
      type: 5,
    }
  },
  computed: {
    // 父订单搜索配置项
    setFiltrateData,
    // 合同提车弹框表单配置项
    dialogDataArr,
    // 父订单列表表头项
    setTableHeadArr,
  },
  methods: {
    /**
     * @description:请求父订单列表数据
     * @param {type}
     * @return:
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      getParentOrderList(data, this.type)
        .then((res) => {
          this.paginationTotal = res.data.totalNum || 0
          this.tableList = res.data.list || []
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
        orderTypes: [this.type],
        orderType: this.type
      }
      changeVariableName(params, 'createTime', true, 'Time')
      const { marketingOrgCodeItems, regionIds } = params
      if (!isEmpty(regionIds)) {
        const len = regionIds.length
        params.serviceOrgCityCode = regionIds[len - 1]
      }
      delete params.regionIds
      if (!isEmpty(params.marketingOrgCodeItems)) {
        const len = marketingOrgCodeItems.length
        params.marketingOrgCodeList = [marketingOrgCodeItems[len - 1]]
      }
      delete params.marketingOrgCodeItems
      this.getCustomerIds(params, true)
      this.searchParams = _.cloneDeep(params)
      return params
    },
    /**
     * @description:将数据格式化处理
     * @param {string} data 当前需要格式化的字符串
     * @return:
     */
    dealData(data) {
      return data !== '0' ? (data ? Number(data) : data) : null
    },
    /**
     * @description:APP下单
     * @param {type}
     * @return:
     */
    orderAPP() {
      this.isLock = true
      getParentOrderList()
        .then(async (res) => {
          this.$message.success('下单成功')
          this.getTableData()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:合同提车
     * @param {type}
     * @return:
     */
    contractCar() {
      this.contractCarFlag = true
    },
    /**
     * @description:关闭弹框
     * @param {type}
     * @return:
     */
    infoHandleClose() {
      this.ruleData = {
        carNumber: '',
        cashDeposit: '',
        performanceCondition: '',
      }
      this.contractCarFlag = false
    },
    /**
     * @description:确认合同提车
     * @param {type}
     * @return:
     */
    saveInfo() {
      this.$refs.dataArrbase.formValidate((res) => {
        if (res) {
          this.isLock = true
          getParentOrderList()
            .then((res) => {
              this.$message.success('合同提车成功')
              this.getTableData()
              this.infoHandleClose()
            })
            .catch(() => {
              this.isLock = false
            })
        }
      })
    },
    /**
     * @description:跳转详情页面
     * @param {type}
     * @return:
     */
    goDetail(row) {
      this.$router.push({
        path: '/escrowCollect/fatherOrders/detail',
        query: {
          id: row.id,
          name: 'BaseInfo',
          code: row.orderCode,
          type: this.type,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped></style>
