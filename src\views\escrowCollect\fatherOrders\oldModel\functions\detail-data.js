/* eslint-disable jsx-quotes */
// import { showData } from '@/utils/business'

/**
 * @description:基础信息1
 * @param {type}
 * @return:
 */
export function baseInfo1(v, key) {
  return [
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'createTime',
      label: '创建时间',
      type: 'text',
    },
    {
      prop: 'payEndTime',
      label: '支付截止时间',
      type: 'text',
    },
    {
      prop: 'cancelTime',
      label: '取消时间',
      type: 'text',
    },
  ]
}

/**
 * @description:基本信息2
 * @param {type}
 * @return:
 */
export function baseInfo2(v, key) {
  return [
    {
      prop: 'orgName',
      label: '运营公司',
      type: 'text',
    },
    {
      prop: 'cityName',
      label: '城市',
      type: 'text',
    },
    {
      prop: 'orderSourceName',
      label: '下单渠道',
      type: 'text',
    },
    {
      prop: 'orderChannel',
      label: '订单渠道',
      type: 'text',
    },
    {
      prop: 'subName',
      label: '集团归属',
      type: 'text',
    },
    {
      prop: 'regionName',
      label: '大区归属',
      type: 'text',
    },
    {
      prop: 'serviceOrgCityName',
      label: '服务组织',
      type: 'text',
    },
    {
      prop: 'marketingOrgName',
      label: '营销组织',
      type: 'text',
    },
    {
      prop: 'customerGroupTypeName',
      label: '业务通路',
      type: 'text',
    },
    {
      prop: 'carNo',
      label: '车牌号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'vinCode',
      label: '车架号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'expDeliveryTime',
      label: '预计交车时间',
      type: 'text',
    },
    {
      prop: 'showSaleName',
      label: '业务归属',
      type: 'text',
    },
    {
      prop: 'performTypeName',
      label: '履约方式',
      type: 'text',
    },
  ]
}
