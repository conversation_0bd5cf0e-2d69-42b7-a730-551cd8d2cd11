import { checkSelect } from '@/portal-common/utils/testedFunc/formValidate'
import { getSimpleDictByType } from '@/api/base'
import DstOrgSelector from '@/portal-common/components/DstOrgSelector/DstOrgSelector'
import { getOrgSplitList } from '@/api/common'
import dictionaries from '@/utils/common.js'
const { orderSource, customerGroupType } = dictionaries

/**
 * @description:父订单查询配置项
 * @param {type}
 * @return:
 */
export function setFiltrateData() {
  return [
    {
      type: 'input',
      prop: 'orderCode',
      label: '父订单编码',
      attrs: {
        placeholder: '请输入父订单编码',
        clearable: true,
      },
      width: '400px',
    },
    {
      type: 'input',
      prop: 'customerId',
      label: '客户编码',
      attrs: {
        placeholder: '请输入客户编码',
        clearable: true,
      },
      width: '400px',
    },
    {
      type: 'select',
      prop: 'customerKeys',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        multiple: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      ...this.filterSearchStyle,
      valueType: [],
      on: { change: (val, data) => this.handleChangeCustomer(data, true) },
    },
    {
      type: 'input',
      prop: 'orderChannel',
      label: '订单渠道',
      attrs: {
        placeholder: '请输入订单渠道',
        clearable: true,
      },
      width: '320px',
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
    },
    {
      type: 'select',
      prop: 'orderSource',
      label: '下单渠道',
      optionsApiConf: {
        parmas: { type: orderSource },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
    },
    {
      type: DstOrgSelector,
      label: '服务组织',
      prop: 'regionIds',
      attrs: {
        // params: { startType: 'REGIONAL_LIST', endType: 'STATION_CITY' },
        customRequest: (vm) => {
          // 自定义接口,没有这个属性则用默认接口
          getOrgSplitList({
            startType: 'REGIONAL_LIST',
            endType: 'STATIONS_CITY',
            isAll: 1,
          }).then((res) => {
            // vm为orgSelector实例
            vm.optionsData = res.data || []
          })
        },
      },
      valueType: [],
      width: '310px',
    },
    {
      type: 'cascader',
      prop: 'marketingOrgCodeItems',
      label: '营销组织',
      attrs: {
        placeholder: '请选择营销组织',
        clearable: true,
        // changeOnSelect: true,
        filterable: true,
        showAllLevels: false,
      },
      options: this.marketingOrgCodeItems,
      valueType: [],
      width: '310px',
      props: {
        value: 'code',
        label: 'name',
        children: 'childs',
      },
    },
    {
      type: 'select',
      prop: 'customerGroupTypeList',
      label: '业务通路',
      optionsApiConf: {
        parmas: { type: customerGroupType },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      attrs: {
        placeholder: '请选择业务通路',
        clearable: true,
        multiple: true,
      },
      valueType: [],
    },
  ]
}

/**
 * @description:合同订单配置项
 * @param {type}
 * @return:
 */
export function dialogDataArr() {
  return [
    {
      prop: 'carNumber',
      label: '提车数量',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
      ],
      rules: [
        {
          required: true,
          validator: checkSelect({ customMessage: '请选择提车数量' }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'cashDeposit',
      label: '保证金',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '收保证金', value: 1 },
        { label: '免保证金', value: 2 },
      ],
      rules: [
        {
          required: true,
          validator: checkSelect({ customMessage: '请选择保证金' }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'performanceCondition',
      label: '履约条件',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '先履约后支付', value: 1 },
        { label: '先履约后结算', value: 2 },
      ],
      rules: [
        {
          required: true,
          validator: checkSelect({ customMessage: '请选择履约条件' }),
          trigger: ['change', 'blur'],
        },
      ],
    },
  ]
}
