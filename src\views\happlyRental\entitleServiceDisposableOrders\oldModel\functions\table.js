import { showData } from '@/utils/business'

/**
 * @description:权益服务（次付）订单列表数据
 * @param {type}
 * @return:
 */
export function setTableHeadArr() {
  return [
    { label: '子订单编码', prop: 'orderCode', minWidth: 220 },
    {
      label: '父订单编码',
      prop: 'parentOrderCode',
      minWidth: 220,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.parentOrderCode,
          data: row,
          orderType: 0,
        })
      },
    },
    { label: '客户编码', prop: 'customerId', minWidth: 180 },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '合同编码', prop: 'contractCode', minWidth: 300 },
    { label: '合同乙方', prop: 'contractPartyName', minWidth: 220 },
    {
      label: '应付金额',
      prop: 'payFee',
      minWidth: 110,
      render(h, scope) {
        return (
          <span>{showData({ data: [scope.row.payFee], frontWord: '￥' })}</span>
        )
      },
    },
    // { label: '商品编码', prop: 'goodsCode', minWidth: 180 },
    // { label: '商品名称', prop: 'goodsName', minWidth: 130 },
    { label: '车牌号', prop: 'carNo', minWidth: 130 },
    { label: '车架号', prop: 'vinCode', minWidth: 220 },
    { label: '创建时间', prop: 'createTime', minWidth: 180 },
    { label: '订单状态', prop: 'orderStatusName', minWidth: 150 },
    // { label: '支付状态', prop: 'payStatusName', minWidth: 110 },
    // { label: '售后状态', prop: 'salesStatusName', minWidth: 110 },
    { label: '履约状态', prop: 'performanceStatusName', minWidth: 110 },
    // { label: '修改价格', prop: 'priceStatusName', minWidth: 130 },
    // { label: '线下收款', prop: 'collectionStatusName', minWidth: 130 },
    // {
    //   label: '订单实际金额',
    //   prop: 'paidFee',
    //   minWidth: 130,
    //   render(h, scope) {
    //     return (
    //       <span>
    //         {showData({ data: [scope.row.paidFee], frontWord: '￥' })}
    //       </span>
    //     )
    //   },
    // },
    {
      label: '订单有效期-开始时间',
      prop: 'validityBeginTime',
      minWidth: 170,
      render: (h, scope) => {
        return (
          <span>
            {showData({
              data: [scope.row.validityBeginTime],
              showEmpty: true,
              spliter: '~',
              type: 'time',
              strict: false,
            })}
          </span>
        )
      },
    },
    {
      label: '订单有效期-结束时间',
      prop: 'validityEndTime',
      minWidth: 170,
      render: (h, scope) => {
        return (
          <span>
            {showData({
              data: [scope.row.validityEndTime],
              showEmpty: true,
              spliter: '~',
              type: 'time',
              strict: false,
            })}
          </span>
        )
      },
    },
    // {
    //   label: '订单开始履约时间',
    //   prop: 'fulfillmentBeginTime',
    //   minWidth: 170,
    //   render: (h, scope) => {
    //     return (
    //       <span>
    //         {showData({
    //           data: [scope.row.fulfillmentBeginTime],
    //           showEmpty: true,
    //           spliter: '~',
    //           type: 'time',
    //           strict: false,
    //         })}
    //       </span>
    //     )
    //   },
    // },
    // {
    //   label: '订单结束履约时间',
    //   prop: 'fulfillmentEndTime',
    //   minWidth: 170,
    //   render: (h, scope) => {
    //     return (
    //       <span>
    //         {showData({
    //           data: [scope.row.fulfillmentEndTime],
    //           showEmpty: true,
    //           spliter: '~',
    //           type: 'time',
    //           strict: false,
    //         })}
    //       </span>
    //     )
    //   },
    // },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '详情',
          click: ({ row }) =>
            this.goDetail({
              value: row.orderCode,
              data: row,
              orderType: row.orderType,
            }),
        },
      ],
    },
  ]
}
