<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalFristServiceOrders',
  components: {
    NewHapplyRentalFristServiceOrders: () => import('./index.vue'),
    OldHapplyRentalFristServiceOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: ''
    }
  },
  created() {
    this.init(
      'OldHapplyRentalFristServiceOrders',
      'NewHapplyRentalFristServiceOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
