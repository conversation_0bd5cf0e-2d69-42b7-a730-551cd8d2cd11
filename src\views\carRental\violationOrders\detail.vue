<template>
  <div class="dst-add-card-wrap dst-form-demo-wrap" v-loading="isLock">
    <div class="main-box">
      <!-- 动态组件集合 -->
      <template v-for="item in componentList">
        <component
          ref="componentItem"
          is-new
          :type="type"
          :is="item"
          :key="item"
          :base-info-form="baseInfoForm"
          :id="orderId"
          :code="orderCode"
          @refresh="refresh"
        ></component>
      </template>
    </div>
  </div>
</template>

<script>
import { BusEmit } from '@/mixins/busEvent'
import BaseInfo from './components/BaseInfo'
import {
  fetchRentalOrderDetail,
  getOrderCategory
} from '@/utils/system/business'
import { isFunction } from '@/utils/types'
import rentalComponents from '@/views/common/childOrders/detailTableList'
const {
  GoodsInfo,
  GiveInfo,
  FeeInfo,
  CustomerInfo,
  AgreeModule
} = rentalComponents
const components = {
  BaseInfo,
  GoodsInfo,
  CustomerInfo,
  GiveInfo,
  FeeInfo,
  AgreeModule
}
export default {
  name: 'NewCarRentalViolationOrderDetail',
  components,
  data() {
    return {
      // loading加载
      isLock: false,
      // 基本信息详情展示
      baseInfoForm: {},
      // 动态组件键值
      componentList: Object.values(components).map(item => item.name)
    }
  },
  computed: {
    // 子订单id
    orderId() {
      return this.$route.query.id
    },
    // 子订单id
    orderCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return (this.orderCode && Number(this.orderCode.slice(0, 2))) || 0
    }
  },
  created() {
    this.getDetailData()
  },
  methods: {
    /**
     * @description:获取页面详情数据
     * @param {function} callback 回调函数
     */
    async getDetailData(callback) {
      this.isLock = true
      try {
        const baseInfoForm = await fetchRentalOrderDetail(this.orderCode, {
          orderCategory: getOrderCategory('violationOrders', 'carRental')
        })
        if (
          baseInfoForm.payType &&
          [1, 2].includes(Number(baseInfoForm.payType))
        ) {
          baseInfoForm.payTypeName += '或自行办理'
        }
        this.baseInfoForm = _.cloneDeep(baseInfoForm)
      } finally {
        isFunction(callback) && callback()
        this.isLock = false
      }
    },
    /**
     * @description:刷新页面数据
     */
    refresh() {
      this.$nextTick(() => {
        this.getDetailData(() => {
          Object.values(components).forEach(element => {
            BusEmit(element.name, 'refresh')
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
