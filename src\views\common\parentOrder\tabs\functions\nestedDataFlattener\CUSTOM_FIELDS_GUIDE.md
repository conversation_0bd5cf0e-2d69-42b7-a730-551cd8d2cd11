# NestedDataFlattener 自定义字段功能指南

## 🎯 功能概述

`NestedDataFlattener` 现在支持为每一项或特定项添加自定义字段，提供了 5 种不同的自定义字段类型：

1. **静态自定义字段** - 为所有项添加相同的字段
2. **动态自定义字段** - 根据每个项的数据动态生成字段
3. **条件自定义字段** - 只为满足特定条件的项添加字段
4. **计算字段** - 基于现有字段计算新字段
5. **字段增强器** - 对现有字段进行增强处理

## 🚀 基础用法

### 1. 静态自定义字段

为所有项添加相同的字段值：

```javascript
const result = new NestedDataFlattener(options)
  .addCustomFields({
    source: 'system',
    processedAt: new Date(),
    version: '1.0.0',
  })
  .flatten(data)
```

### 2. 动态自定义字段

根据每个项的数据动态生成字段：

```javascript
const result = new NestedDataFlattener(options)
  .addCustomFields((item, parent, context) => {
    return {
      uniqueId: `${parent?.id || 'root'}-${item.id}`,
      level: context.type === 'parent' ? 0 : 1,
      amountLevel: item.amount > 1000 ? 'high' : 'low',
    }
  })
  .flatten(data)
```

### 3. 条件自定义字段

只为满足条件的项添加字段：

```javascript
const result = new NestedDataFlattener(options)
  .addCustomFields(
    {
      isHighValue: true,
      priority: 'urgent',
    },
    {
      condition: (item) => item.amount > 1000,
    }
  )
  .flatten(data)
```

### 4. 计算字段

基于现有字段计算新字段：

```javascript
const result = new NestedDataFlattener(options)
  .addComputedField('tax', (item) => {
    return Math.round(item.amount * 0.1)
  })
  .addComputedField('totalAmount', (item) => {
    return item.amount + (item.tax || 0)
  })
  .flatten(data)
```

### 5. 字段增强器

对现有字段进行增强处理：

```javascript
const result = new NestedDataFlattener(options)
  // 增强特定字段
  .addFieldEnhancer('name', (value, fieldName, item) => {
    return `[${item.status.toUpperCase()}] ${value}`
  })
  // 使用正则匹配字段
  .addFieldEnhancer(/.*Amount$/, (value) => {
    return `¥${value.toLocaleString()}`
  })
  .flatten(data)
```

## 📊 在 OrderAdjustment.vue 中的应用

### 基础应用

```javascript
// 在 getTableData 方法中
getTableData() {
  this.isLock = true
  getParentOrderAdjustOrderList({
    parentOrderCode: this.parentCode,
    pageSize: 9999,
    pageNum: 1
  })
    .then(res => {
      const orderAdjustData = res.data.list || []

      this.tableList = NestedDataFlattener
        .create('orderAdjustment')
        // 添加处理信息
        .addCustomFields({
          processedBy: this.$store.state.user.name,
          processedAt: new Date().toISOString(),
          source: 'order-adjustment-page'
        })
        // 添加动态字段
        .addCustomFields((item, parent) => ({
          fullAdjustCode: `${parent?.adjustCode || 'UNKNOWN'}_${item.id}`,
          hasParent: !!parent,
          itemType: parent ? 'adjustment-item' : 'adjustment-order'
        }))
        .flatten(orderAdjustData)
    })
    .finally(() => {
      this.isLock = false
    })
}
```

### 高级应用

```javascript
getTableData() {
  this.isLock = true
  getParentOrderAdjustOrderList({
    parentOrderCode: this.parentCode,
    pageSize: 9999,
    pageNum: 1
  })
    .then(res => {
      const orderAdjustData = res.data.list || []

      this.tableList = NestedDataFlattener
        .create('orderAdjustment')
        // 添加基础信息
        .addCustomFields({
          batchId: `BATCH_${Date.now()}`,
          pageSource: 'OrderAdjustment',
          userId: this.$store.state.user.id
        })
        // 动态生成业务字段
        .addCustomFields((item, parent, context) => ({
          // 完整路径
          fullPath: parent ? `${parent.adjustCode} > ${item.id}` : item.adjustCode,
          // 层级信息
          level: context.type === 'parent' ? 0 : 1,
          // 唯一标识
          uniqueKey: `${this.parentCode}_${item.id}_${Date.now()}`
        }))
        // 为高金额调整添加特殊标记
        .addCustomFields(
          {
            requiresApproval: true,
            isHighRisk: true,
            notificationRequired: true
          },
          {
            condition: (item) => Math.abs(item.adjustAmount || 0) > 1000
          }
        )
        // 计算调整影响等级
        .addComputedField('adjustImpact', (item) => {
          const amount = Math.abs(item.adjustAmount || 0)
          if (amount > 1000) return 'high'
          if (amount > 500) return 'medium'
          return 'low'
        })
        // 计算状态描述
        .addComputedField('statusDescription', (item) => {
          const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'cancelled': '已取消'
          }
          return statusMap[item.status] || '未知状态'
        })
        // 增强显示字段
        .addFieldEnhancer('adjustAmount', (value) => {
          if (!value) return '¥0'
          const formatted = Math.abs(value).toLocaleString()
          return value > 0 ? `+¥${formatted}` : `-¥${formatted}`
        })
        .flatten(orderAdjustData)
    })
    .finally(() => {
      this.isLock = false
    })
}
```

## 🎨 实用场景

### 场景 1：添加操作按钮配置

```javascript
.addCustomFields((item, parent) => ({
  // 根据状态决定可用操作
  availableActions: item.status === 'pending'
    ? ['edit', 'approve', 'reject']
    : ['view'],
  // 是否可编辑
  editable: item.status === 'pending' && item.adjustAmount < 1000,
  // 按钮样式
  actionButtonType: item.status === 'completed' ? 'success' : 'primary'
}))
```

### 场景 2：添加权限控制

```javascript
.addCustomFields((item, parent, context) => ({
  // 当前用户是否有权限查看
  canView: this.checkPermission('view', item),
  // 当前用户是否有权限编辑
  canEdit: this.checkPermission('edit', item) && item.status === 'pending',
  // 当前用户是否有权限审批
  canApprove: this.checkPermission('approve', item) && item.requiresApproval
}))
```

### 场景 3：添加显示格式化

```javascript
.addComputedField('displayName', (item, parent) => {
  return parent ? `${parent.adjustCode} - ${item.itemName}` : item.adjustCode
})
.addComputedField('displayAmount', (item) => {
  const amount = item.adjustAmount || 0
  return amount >= 0 ? `+${amount}` : `${amount}`
})
.addComputedField('displayStatus', (item) => {
  const statusConfig = {
    pending: { text: '待处理', color: 'orange' },
    processing: { text: '处理中', color: 'blue' },
    completed: { text: '已完成', color: 'green' }
  }
  return statusConfig[item.status] || { text: '未知', color: 'gray' }
})
```

## 🔧 API 参考

### addCustomFields(fields, options)

添加自定义字段

**参数：**

- `fields` - Object|Function: 字段配置或生成函数
- `options.condition` - Function: 条件函数
- `options.override` - Boolean: 是否覆盖已存在字段

### addComputedField(fieldName, computeFunction, options)

添加计算字段

**参数：**

- `fieldName` - String: 字段名
- `computeFunction` - Function: 计算函数 `(item, parent, context) => value`
- `options.override` - Boolean: 是否覆盖已存在字段

### addFieldEnhancer(matcher, enhancer)

添加字段增强器

**参数：**

- `matcher` - Function|String|RegExp: 字段匹配器
- `enhancer` - Function: 增强函数 `(value, fieldName, item, context) => newValue`

### 静态方法：withCustomFields(data, options, customFields)

快速创建带自定义字段的扁平化

**参数：**

- `data` - Array: 数据
- `options` - Object: 配置选项
- `customFields` - Object|Function: 自定义字段

## 💡 最佳实践

1. **性能考虑**：避免在自定义字段函数中进行复杂计算
2. **错误处理**：自定义字段函数中的错误会被自动捕获和记录
3. **字段命名**：使用有意义的字段名，避免与现有字段冲突
4. **条件优化**：条件函数应该尽可能简单和快速
5. **链式调用**：合理使用链式调用提高代码可读性

## 🎉 总结

自定义字段功能让 `NestedDataFlattener` 更加灵活和强大，可以：

- ✅ 为所有项或特定项添加自定义字段
- ✅ 支持静态、动态、条件、计算和增强等多种方式
- ✅ 完全向后兼容，不影响现有功能
- ✅ 提供丰富的 API 和链式调用支持
- ✅ 自动错误处理和性能优化

现在您可以根据业务需求灵活地为数据添加各种自定义字段！
