import { isEmpty, get<PERSON>hain } from '@/utils'
import { showData } from '@/utils/business'
import { queryOrderSearchSubItem } from '@/api/common/rental/childs.js'
import {
  ORDER_CATEGORIES,
  BUSINESS_TYPE_DEFAULT_CATEGORIES,
} from '@/utils/system/constant'

/**
 * 生成请求参数对象，根据传入的数据、选项和配置进行参数构造
 *
 * @param {Object} arg - 参数对象
 * @param {Object} [arg.data=originData] - 原始数据对象，包含各种查询条件字段
 * @param {Object} [arg.options] - 请求选项配置
 * @param {number[]} [arg.options.queryItems=[1,2,3,4,5,6,7,8,9]] - 查询项目枚举集合（1-订单基础信息...9-结算信息）
 * @param {string} [arg.options.orderBusinessType='service'] - 订单业务类型枚举：租赁/售车/服务/以租代售
 * @param {Object} [arg.options.subOrder] - 子订单配置
 * @param {number} [arg.options.subOrder.orderCategory=6] - 子订单分类编号
 * @returns {Object} 构造后的完整请求参数对象，包含分页配置、查询条件和业务类型参数
 */
export function getParams({
  data,
  options = {},
  orderCategory = 6,
  // rent-租赁、sale-售车、service-服务、rent_for_sale-以租代售
  orderBusinessType = 'service',
  // 1-订单基础信息、2-客户信息、3-合同信息、4.履约信息、5-商品信息、6-优惠信息、7-赠送信息、8-销售信息、9-结算信息
  queryItems = [1, 2, 3, 4, 5, 6, 7, 8, 9],
}) {
  // 解构常用查询条件字段
  const {
    customerId,
    carNoList,
    vinCode,
    contractCode,
    orderStatus,
    createStartTime,
    createEndTime,
    orderCode,
    subOrderCode,
    goodsCode,
    goodsName,
    performType,
    performStatus,
    billingMethod,
    expireStartTimeBegin,
    expireStartTimeEnd,
    expireEndTimeBegin,
    expireEndTimeEnd,
    orderType,
    orderCodeList,
    subOrderCodeList,
    orderTypeList,
  } = _.cloneDeep(data) || {}

  // 合并策略：options配置 > 独立参数 > 默认值
  const mergedOptions = {
    ...options,
    orderBusinessType: options.orderBusinessType || orderBusinessType,
    queryItems: options.queryItems || queryItems,
    subOrder: {
      orderCategory:
        options.subOrder && options.subOrder.orderCategory
          ? options.subOrder.orderCategory
          : orderCategory,
      ...options.subOrder, // 保留其他subOrder配置
    },
  }

  const params = {
    ...mergedOptions,
    subOrder: mergedOptions.subOrder ? _.cloneDeep(mergedOptions.subOrder) : {},
  }

  // 构建子订单查询条件
  const subOrder = params.subOrder
  if (orderCode) subOrder.orderCodeList = [orderCode]
  if (!isEmpty(orderCodeList)) subOrder.orderCodeList = orderCodeList || []
  if (subOrderCode) subOrder.subOrderCodeList = [subOrderCode]
  if (!isEmpty(subOrderCodeList)) {
    subOrder.subOrderCodeList = subOrderCodeList || []
  }
  if (orderStatus) subOrder.orderStatusList = [orderStatus]
  if (createStartTime) subOrder.createStartTime = createStartTime
  if (createEndTime) subOrder.createEndTime = createEndTime
  if (billingMethod) subOrder.billingMethod = billingMethod
  if (expireStartTimeBegin) subOrder.expireStartTimeBegin = expireStartTimeBegin
  if (expireStartTimeEnd) subOrder.expireStartTimeEnd = expireStartTimeEnd
  if (expireEndTimeBegin) subOrder.expireEndTimeBegin = expireEndTimeBegin
  if (expireEndTimeEnd) subOrder.expireEndTimeEnd = expireEndTimeEnd
  if (orderType) subOrder.orderTypeList = [orderType]
  if (orderTypeList) subOrder.orderTypeList = orderTypeList || []

  // 处理客户相关查询条件
  if (customerId) {
    params.customer = params.customer || {}
    params.customer.customerIdList = [customerId]
  }

  // 构建车辆相关查询参数
  if (!isEmpty(carNoList) || vinCode) {
    params.carGoods = params.carGoods || {}
    if (!isEmpty(carNoList)) params.carGoods.carNoList = carNoList
    if (vinCode) params.carGoods.vinCode = vinCode
  }

  // 构建商品相关查询参数
  if (goodsCode || goodsName) {
    params.virtualGoods = params.virtualGoods || {}
    if (goodsCode) params.virtualGoods.goodsCode = goodsCode
    if (goodsName) params.virtualGoods.goodsName = goodsName
  }

  // 添加合同编码查询条件
  if (contractCode) {
    params.contract = params.contract || {}
    params.contract.contractCode = contractCode
  }

  // 构建履约信息查询参数
  if (performType || performStatus) {
    params.perform = params.perform || {}
    if (performType) params.perform.performTypeList = [performType]
    if (performStatus) params.perform.performStatusList = [performStatus]
  }

  return params
}

/**
 * 处理订单数据，将子订单及相关信息合并为扁平化结构
 *
 * @param {Object} res - 接口响应数据对象
 * @param {Object} res.data - 数据主体
 * @param {Array} [res.data.list=[]] - 订单列表数据，默认为空数组
 * @returns {Array} 处理后的订单数据数组，每个元素合并了子订单、客户信息、商品信息等
 */
export function setPageData(res, isDetail) {
  // 获取订单列表数据，处理空值情况
  const tableList = (isDetail ? res.data : res.data.list) || []

  // 将嵌套的多层对象合并为扁平数据结构
  const result = tableList.map((item) => {
    // 解构商品信息（包含实物商品和虚拟商品）
    const { carGoods, virtualGoods } = item.goods || {}
    let payFee
    const attrForm = {}
    if (item.subOrder) {
      payFee = item.subOrder.totalAmount
      const attrs = item.subOrder.attributes || []
      const performs = item.performs.attributes || []
      const propList = [...attrs, ...performs]
      propList.forEach((item) => {
        attrForm[item.key] = item.value
      })
    }
    const [performItem] = item.performs || []
    const virtualGoodsName = (virtualGoods || [])
      .map((item) => item.goodsName)
      .join(',')
    const virtualGoodsCode = (virtualGoods || [])
      .map((item) => item.goodsCode)
      .join(',')

    // 合并多个相关对象属性：
    // 1. 子订单基础信息 (subOrder)
    // 2. 客户信息 (customer)
    // 3. 实物商品信息 (carGoods)
    // 4. 虚拟商品信息 (virtualGoods)
    // 5. 合同信息 (contract)
    // 6. 履约信息 (performs)
    return {
      ...item.subOrder,
      ...item.contract,
      ...item.customer,
      ...item.performs,
      ...carGoods,
      ...attrForm,
      ...performItem,
      payFee,
      performs: item.performs || [],
      goods: virtualGoods || [],
      goodsName: virtualGoodsName,
      goodsCode: virtualGoodsCode,
    }
  })
  if (isDetail) {
    const [item] = result || []
    return item || {}
  } else {
    return result || []
  }
}

/**
 * 获取父级订单查询参数
 *
 * 该函数用于构建订单查询的请求参数对象，支持多种查询条件和配置选项的合并。
 *
 * @param {Object} params - 参数对象
 * @param {Object} params.data - 原始查询数据
 * @param {Object} [params.options={}] - 自定义配置选项，优先级高于默认值
 * @param {Array<number>} [params.orderTypeList=[2,3]] - 订单类型列表（默认值：2,3）
 * @param {string} [params.orderBusinessType='service'] - 业务类型枚举：rent-租赁/sale-售车/service-服务/rent_for_sale-以租代售
 * @param {Array<number>} [params.queryItems=[1,2,3,4,5]] - 查询项枚举：1-订单基础信息/2-客户信息/3-合同信息/4-履约信息/5-商品信息/6-优惠信息/7-赠送信息/8-销售信息/9-结算信息
 * @returns {Object} 构建完成的查询参数对象
 */
export function getParentParams({
  data,
  options = {},
  orderTypeList = [2, 3],
  // rent-租赁、sale-售车、service-服务、rent_for_sale-以租代售
  orderBusinessType = 'service',
  // 1-订单基础信息、2-客户信息、3-合同信息、4.履约信息、5-商品信息、6-优惠信息、7-赠送信息、8-销售信息、9-结算信息
  queryItems = [1, 2, 3, 4, 5],
}) {
  // 深拷贝并解构常用查询字段
  const {
    carNoList,
    vinCode,
    contractCode,
    orderStatus,
    createTimeStart,
    createTimeEnd,
    orderCode,
    customerIds,
    orderChannel,
    orderSource,
    marketingOrgCodeItems,
    regionIds,
    customerGroupTypeList,
    originalParentCodes,
    orderCodeList,
    subOrderCodeList,
    subOrderCode,
    shopId,
    contractCodes,
    carUse,
    depositFlag,
  } = _.cloneDeep(data) || {}

  // 合并配置策略：options配置 > 独立参数 > 默认值
  const mergedOptions = {
    ...options,
    orderBusinessType: options.orderBusinessType || orderBusinessType,
    queryItems: options.queryItems || queryItems,
    orderBasic: {
      orderTypeList:
        options.orderBasic && !isEmpty(options.orderBasic.orderTypeList)
          ? options.orderBasic.orderTypeList
          : orderTypeList,
      ...options.orderBasic, // 保留其他subOrder配置
    },
  }

  // 初始化参数对象
  const params = {
    ...mergedOptions,
    orderBasic: mergedOptions.orderBasic
      ? _.cloneDeep(mergedOptions.orderBasic)
      : {},
  }

  // 处理订单基础信息查询条件
  const basicList = [
    orderCode,
    orderCodeList,
    subOrderCode,
    subOrderCodeList,
    orderStatus,
    createTimeStart,
    createTimeEnd,
    orderChannel,
    orderSource,
    originalParentCodes,
    depositFlag,
  ]
  const flag = basicList.some((item) => !isEmpty(item))
  if (flag) params.orderBasic = {}
  if (orderCode) params.orderBasic.orderCodeList = [orderCode]
  if (!isEmpty(orderCodeList)) {
    params.orderBasic.orderCodeList = orderCodeList || []
  }
  if (subOrderCode) params.orderBasic.subOrderCodeList = [subOrderCode]
  if (!isEmpty(subOrderCodeList)) {
    params.orderBasic.subOrderCodeList = subOrderCodeList || []
  }
  if (createTimeStart) params.orderBasic.createTimeStart = createTimeStart
  if (createTimeEnd) params.orderBasic.createTimeEnd = createTimeEnd
  if (orderStatus) params.orderBasic.orderStatusList = [orderStatus]
  if (orderChannel) params.orderBasic.orderChannelList = [orderChannel]
  if (orderSource) params.orderBasic.orderSourceList = [orderSource]
  if (!isEmpty(originalParentCodes)) {
    params.orderBasic.originalOrderCodeList = originalParentCodes
  }
  if (depositFlag) params.orderBasic.depositFlag = depositFlag

  // 处理客户信息查询条件
  const customerList = [
    customerIds,
    marketingOrgCodeItems,
    regionIds,
    customerGroupTypeList,
    shopId,
  ]
  const customerFlag = customerList.some((item) => !isEmpty(item))
  if (customerFlag) params.customer = {}
  if (!isEmpty(customerIds)) params.customer.customerIdList = customerIds
  if (!isEmpty(regionIds)) {
    params.customer.serviceOrgCityCode = regionIds[regionIds.length - 1]
  }
  if (!isEmpty(marketingOrgCodeItems)) {
    params.customer.marketingCityCode =
      marketingOrgCodeItems[marketingOrgCodeItems.length - 1]
  }
  if (!isEmpty(customerGroupTypeList)) {
    params.customer.customerGroupTypeList = customerGroupTypeList
  }
  if (shopId) params.customer.shopIdList = [shopId]

  // 处理车辆商品信息
  const carGoodList = [carNoList, vinCode, carUse]
  const carGoodFlag = carGoodList.some((item) => !isEmpty(item))
  if (carGoodFlag) params.carGoods = {}
  if (!isEmpty(carNoList)) params.carGoods.carNoList = carNoList
  if (vinCode) params.carGoods.vinCode = vinCode
  if (carUse) params.carGoods.carUse = carUse

  // 处理合同信息
  const contractValue = contractCode || contractCodes
  if (contractValue) {
    params.contract = {}
    params.contract.contractCode = contractValue
  }

  return params
}

/**
 * 处理父页面数据，根据是否为详情页返回不同结构的数据
 * @param {Object} res - 接口返回的原始数据
 * @param {boolean} isDetail - 是否为详情页模式
 * @param {Object} [options={}] - 可选配置项，包含营销组织和服务组织的数据
 * @param {Array} options.marketingOrgCodeItems - 营销组织数据
 * @param {Array} options.serviceOrgCodeItems - 服务组织数据
 * @returns {Object|Array} - 返回处理后的数据，详情页返回对象结构，列表页返回数组结构
 */
export function setParentPageData(res, isDetail, options = {}) {
  // 处理订单列表数据，适配详情和列表两种场景
  const tableList = (isDetail ? res.data : res.data.list) || []

  /**
   * 扁平化处理嵌套数据结构
   * 1. 合并商品、客户、履约等嵌套对象
   * 2. 处理营销组织和服务组织的层级名称
   * 3. 转换属性字段为键值对形式
   */
  const result = tableList.map((item) => {
    const { goods, customer, performs, orderBasic, contract, salemans } =
      item || {}
    const { carGoods } = goods || {}
    const [saleman] = salemans || []
    const jobNum = saleman ? saleman.jobNum || '' : ''
    const saleName = saleman ? saleman.name || '' : ''
    const showSaleName = saleName + (jobNum ? '/' + jobNum : '')

    // 处理营销组织名称的层级展示
    let marketingOrgName = ''
    if (customer.marketingCityCode) {
      const marketingCityInfo = getChain({
        value: customer.marketingCityCode,
        data: options.marketingOrgCodeItems || [],
        props: { id: 'orgCode', name: 'name', children: 'childs' },
      })
      if (!isEmpty(marketingCityInfo.items)) {
        marketingOrgName = marketingCityInfo.items
          .map((item) => item.name)
          .join('/')
      }
    } else if (customer.marketingOrgCode) {
      const marketingOrgInfo = getChain({
        value: customer.marketingOrgCode,
        data: options.marketingOrgCodeItems || [],
        props: { id: 'code', name: 'name', children: 'childs' },
      })
      if (!isEmpty(marketingOrgInfo.items)) {
        marketingOrgName = marketingOrgInfo.items
          .map((item) => item.name)
          .join('/')
      }
    }

    // 处理服务组织城市名称
    const { data: orgItem } = getChain({
      value: customer.serviceOrgCityCode,
      data: options.serviceOrgCodeItems || [],
      props: { id: 'orgCode', name: 'name', children: 'childs' },
    })
    let serviceOrgCityName = ''
    if (orgItem) {
      serviceOrgCityName = orgItem.name || ''
    }

    // 合并订单基础属性和履约属性
    const attrForm = {}
    if (orderBasic) {
      const attrs = orderBasic.attributes || []
      attrs.forEach((basicItem) => {
        attrForm[basicItem.key] = basicItem.value
      })
    }
    if (performs) {
      const attrs = performs.attributes || []
      attrs.forEach((performItem) => {
        attrForm[performItem.key] = performItem.value
      })
    }

    // 处理履约方式显示名称
    const [performItem] = performs || []
    let billingFulfillMethodName = '场站履约计费'
    if (performItem && performItem.billingFulfillMethodName) {
      billingFulfillMethodName = performItem.billingFulfillMethodName
    }

    // 返回合并后的扁平化数据结构
    return {
      ...orderBasic,
      ...customer,
      ...contract,
      ...carGoods,
      ...attrForm,
      marketingOrgName,
      serviceOrgCityName,
      showSaleName,
      customerGroupTypeName: customer.customerGroupTypeName,
      performTypeName: performItem ? performItem.performTypeName : '',
      billingFulfillMethodName,
      salemans: (salemans || []).map((item) => ({
        ...item,
        saleName: item.name,
      })),
    }
  })

  // 详情页返回特殊结构
  if (isDetail) {
    const [item] = result || []
    const [basicItem] = !isEmpty(tableList) ? tableList : [{}]
    const resultItem = {
      gives: basicItem.gives || [],
      coupons: basicItem.coupons || [],
    }

    // 处理客户属性字段
    const attrForm = {}
    const attrs = basicItem.customer.attributes || []
    attrs.forEach((attrCustomerItem) => {
      attrForm[attrCustomerItem.key] = attrCustomerItem.value
    })

    // 合并客户信息
    const customerInfo = {
      ...basicItem.customer,
      ...attrForm,
      conCustomerId: basicItem.firstPartyId || '',
      conCustomerName: basicItem.firstPartyName || '',
    }

    return {
      baseInfo: { ...item, ...resultItem },
      customerInfo,
    }
  } else {
    // 列表页直接返回结果数组
    return result || []
  }
}

/**
 * 格式化显示时间数据
 *
 * 该函数是一个包装函数，用于将时间值格式化为可显示的字符串形式。
 * 它通过调用showData函数并传入特定配置来实现时间格式化。
 *
 * @param {*} value - 需要格式化的时间值，可以是Date对象、时间戳或时间字符串等
 * @returns {string} 返回格式化后的时间字符串
 *
 * 配置参数说明：
 * - data: 要格式化的原始时间数据
 * - showEmpty: 允许显示空值
 * - spliter: 时间范围分隔符（当value是时间范围时使用）
 * - type: 指定数据类型为'time'，用于特殊的时间格式化处理
 * - strict: 非严格模式，允许更灵活的格式转换
 */
export function showTime(value) {
  // 调用底层showData函数进行实际的时间格式化处理
  return showData({
    data: value,
    showEmpty: true,
    spliter: '~',
    type: 'time',
    strict: false,
  })
}

/**
 * 获取订单详情信息
 *
 * @async
 * @function fetchOrderDetail
 * @param {string} orderCode - 订单编号
 * @param {Object} [config={}] - 可选配置参数，会覆盖默认配置
 * @param {number} [config.orderCategory] - 订单分类
 * @param {Array<number>} [config.queryItems] - 需要查询的订单项
 * @param {string} [config.orderBusinessType] - 订单业务类型
 * @returns {Promise<Object>} 处理后的订单详情数据
 */
export async function fetchOrderDetail(orderCode, config = {}) {
  // 合并参数：优先级为 config > { subOrderCode: orderCode }
  const params = getParams({
    data: { subOrderCode: orderCode },
    queryItems: [1, 2, 3, 4, 5],
    ...config,
  })

  // 查询订单详情并返回处理后的数据
  const res = await queryOrderSearchSubItem(params)
  return setPageData(res, true)
}

/**
 * 获取租赁订单详情
 *
 * 该函数是一个异步函数，用于获取特定租赁订单的详细信息。它通过调用通用的fetchOrderDetail函数，
 * 并传入特定的订单类别和查询参数来实现。
 *
 * @param {string} orderCode - 订单编号，用于指定要查询的订单
 * @param {Object} [config={}] - 可选配置对象，用于覆盖或扩展默认的查询参数
 * @returns {Promise} 返回一个Promise对象，解析后将包含订单详情数据
 */
export async function fetchRentalOrderDetail(orderCode, config = {}) {
  // 调用通用订单详情获取函数，传入租赁订单特定的参数配置
  // 默认参数包括：订单类别7(租赁)，查询项目[1,2,3,4,5]，业务类型'rent'
  // 允许通过config参数覆盖或扩展这些默认配置
  return fetchOrderDetail(orderCode, {
    queryItems: [1, 2, 3, 4, 5],
    orderBusinessType: 'rent',
    ...config,
  })
}

/**
 * 获取服务订单详情
 *
 * 这是一个异步函数，用于获取指定服务订单的详细信息。它通过调用[fetchOrderDetail](file://e:\CODE\portal-order-center.dstcar.com\src\utils\system\business.js#L498-L508)函数，
 * 并传入特定的查询参数和配置来获取订单详情。
 *
 * @param {string} orderCode - 订单编号，用于唯一标识要查询的订单
 * @param {Object} [config={}] - 可选配置对象，用于覆盖或扩展默认的查询参数
 * @returns {Promise} 返回一个Promise对象，解析为订单详情数据
 *
 * 注意：默认查询参数包括queryItems为[1,2,3,5]和orderBusinessType为'service'，
 * 这些默认值会被传入的config参数覆盖或扩展
 */
export async function fetchServiceOrderDetail(orderCode, config = {}) {
  return fetchOrderDetail(orderCode, {
    queryItems: [1, 2, 3, 4, 5], // 默认查询的字段项
    orderBusinessType: 'service', // 订单业务类型固定为服务类
    ...config, // 合并用户自定义配置
  })
}

/**
 * 获取订单类型对应的 orderCategory
 * @param {string} orderType - 订单类型键名
 * @param {string} businessType - 业务类型 (consignmentRental|carRental|happlyRental|equityServices|escrowCollect)
 * @returns {number} orderCategory 值
 */
export const getOrderCategory = (
  orderType,
  businessType = 'consignmentRental'
) => {
  return (
    ORDER_CATEGORIES[orderType] ||
    BUSINESS_TYPE_DEFAULT_CATEGORIES[businessType] ||
    1
  )
}

// 各个模式下的订单类别处理器
export const getConsignmentRentalOrderCategory = (orderType) =>
  getOrderCategory(orderType, 'consignmentRental')

export const getCarRentalOrderCategory = (orderType) =>
  getOrderCategory(orderType, 'carRental')

export const getHapplyRentalOrderCategory = (orderType) =>
  getOrderCategory(orderType, 'happlyRental')

export const getEquityServicesOrderCategory = (orderType) =>
  getOrderCategory(orderType, 'equityServices')

export const getEscrowCollectOrderCategory = (orderType) =>
  getOrderCategory(orderType, 'escrowCollect')
