/* eslint-disable jsx-quotes */
import { getSimpleDictByType } from '@/api/base'
import BatchInput from '@/portal-common/components/BatchSearch'
import dictionaries from '@/utils/common.js'
const { orderStatus } = dictionaries

/**
 * @description:权益服务（月付）订单查询配置项
 */
export function setFiltrateData() {
  return [
    {
      type: 'input',
      prop: 'orderCode',
      label: '子订单编码',
      attrs: {
        placeholder: '请输入子订单编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'parentOrderCode',
      label: '父订单编码',
      attrs: {
        placeholder: '请输入父订单编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerKey',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      on: { change: (val, data) => this.handleChangeCustomer(data, false) },
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'contractCode',
      label: '合同编码',
      attrs: {
        placeholder: '请输入合同编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'goodsCode',
      label: '商品编码',
      attrs: {
        placeholder: '请输入商品编码',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'goodsName',
      label: '商品名称',
      attrs: {
        placeholder: '请输入商品名称',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: BatchInput,
      prop: 'carNoList',
      label: '车牌号',
      attrs: {
        placeholder: '请输入车牌号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'vinCode',
      label: '车架号',
      attrs: {
        placeholder: '请输入车架号',
        clearable: true,
        width: '320px',
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderStatus',
      label: '订单状态',
      optionsApiConf: {
        parmas: { type: orderStatus },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px'
    },
    {
      type: 'DatePicker',
      prop: 'validityBeginTime',
      label: (h) => {
        return (
          <div>
            <span class="label-left">订单有效期-开始时间</span>
            <i
              class={`el-icon-question`}
              onClick={this.view.bind(this, 'Validity')}
            ></i>
          </div>
        )
      },
      labelWidth: '180px',
      width: '440px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
    },
    {
      type: 'DatePicker',
      prop: 'validityEndTime',
      labelWidth: '180px',
      label: (h) => {
        return (
          <div>
            <span class="label-left">订单有效期-结束时间</span>
            <i
              class={`el-icon-question`}
              onClick={this.view.bind(this, 'Validity')}
            ></i>
          </div>
        )
      },
      width: '440px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
    },
    {
      type: 'checkbox',
      prop: 'aggregation',
      label: '按父订单聚合',
    },
  ]
}
