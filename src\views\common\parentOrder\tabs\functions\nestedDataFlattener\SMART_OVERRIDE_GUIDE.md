# NestedDataFlattener 智能覆盖功能指南

## 概述

`NestedDataFlattener` 现在支持智能覆盖模式，可以避免用空值覆盖有意义的数据。这个功能特别适用于数据合并时需要保留非空值的场景。

## 新增功能

### 1. 智能覆盖策略 (smart-override)

这是一个全新的合并策略，专门用于智能处理空值覆盖问题：

```javascript
const flattener = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority'],
  mergeStrategy: 'smart-override',
  preserveNonEmptyValues: true
})
```

**智能覆盖规则：**
- 如果子项没有该字段，直接使用父项值
- 如果子项值为空但父项值非空，使用父项值
- 如果两个值都非空，根据 `preserveNonEmptyValues` 配置决定是否覆盖
- 其他情况保持子项值不变

### 2. 保留非空值选项 (preserveNonEmptyValues)

这个选项可以与任何合并策略配合使用，防止用空值覆盖有值的字段：

```javascript
const flattener = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority'],
  mergeStrategy: 'parent-first',
  preserveNonEmptyValues: true // 启用保留非空值
})
```

## 使用场景

### 场景1：数据清洗和补全

当您有不完整的数据需要用其他数据源补全时：

```javascript
// 原始数据
const data = [
  {
    id: 1,
    customerName: '张三',
    phone: '', // 父项电话为空
    email: '<EMAIL>',
    children: [
      {
        id: 11,
        customerName: '', // 子项姓名为空
        phone: '13800138000', // 子项电话有值
        email: '' // 子项邮箱为空
      }
    ]
  }
]

// 使用智能覆盖
const flattener = new NestedDataFlattener({
  parentFields: ['customerName', 'phone', 'email'],
  mergeStrategy: 'smart-override',
  preserveNonEmptyValues: true
})

const result = flattener.flatten(data)
// 结果：
// {
//   id: 11,
//   customerName: '张三',    // 使用父项值（子项为空）
//   phone: '13800138000',   // 保持子项值（子项非空）
//   email: '<EMAIL>' // 使用父项值（子项为空）
// }
```

### 场景2：配置继承

当子项需要继承父项配置，但不希望父项的空配置覆盖子项的有效配置：

```javascript
const configData = [
  {
    id: 1,
    theme: 'dark',
    language: '', // 父项语言未设置
    timeout: 30,
    children: [
      {
        id: 11,
        theme: '', // 子项主题未设置
        language: 'zh-CN', // 子项语言已设置
        timeout: null // 子项超时未设置
      }
    ]
  }
]

const result = new NestedDataFlattener({
  parentFields: ['theme', 'language', 'timeout'],
  mergeStrategy: 'smart-override'
}).flatten(configData)

// 结果：theme 使用父项值，language 保持子项值，timeout 使用父项值
```

## 配置选项详解

### mergeStrategy 选项

1. **'child-first'** (默认)
   - 子项优先，父项不覆盖子项已有字段
   - 配合 `preserveNonEmptyValues: true` 时，只有子项值为空才使用父项值

2. **'parent-first'**
   - 父项优先，父项字段覆盖子项相同字段
   - 配合 `preserveNonEmptyValues: true` 时，只有父项值非空才覆盖子项值

3. **'smart-override'** (新增)
   - 智能覆盖，专门处理空值覆盖问题
   - 自动应用智能覆盖逻辑，避免用空值覆盖有值字段

4. **'merge'**
   - 字段级别的精确控制
   - 配合 `preserveNonEmptyValues: true` 时，在字段优先级基础上应用空值保护

### preserveNonEmptyValues 选项

- **true**: 启用非空值保护，避免用空值覆盖有值字段
- **false**: 禁用保护，按原有逻辑进行覆盖

## 空值判断规则

系统将以下值视为"空值"：
- `null`
- `undefined`
- `''` (空字符串)
- `[]` (空数组)
- `{}` (空对象)

## 实际应用示例

### 订单数据处理

```javascript
// 处理订单数据，避免用空的父项信息覆盖有效的子项信息
const orderFlattener = new NestedDataFlattener({
  parentFields: ['customerName', 'customerPhone', 'deliveryAddress', 'remark'],
  mergeStrategy: 'smart-override',
  preserveNonEmptyValues: true
})
```

### 用户配置合并

```javascript
// 合并用户配置，保留用户的个性化设置
const configFlattener = new NestedDataFlattener({
  parentFields: ['theme', 'language', 'notifications', 'privacy'],
  mergeStrategy: 'child-first',
  preserveNonEmptyValues: true
})
```

### 产品信息补全

```javascript
// 补全产品信息，避免用空的分类信息覆盖产品特有信息
const productFlattener = new NestedDataFlattener({
  parentFields: ['categoryName', 'brand', 'supplier', 'warranty'],
  mergeStrategy: 'parent-first',
  preserveNonEmptyValues: true
})
```

## 链式调用支持

新功能完全支持链式调用：

```javascript
const result = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority']
})
  .setPriorityStrategy('smart-override', {
    preserveNonEmptyValues: true
  })
  .flatten(data)
```

## 性能考虑

- 智能覆盖功能会增加少量的性能开销（主要是空值判断）
- 对于大量数据处理，建议在开发环境中测试性能影响
- 如果不需要空值保护，可以使用传统的合并策略以获得更好的性能

## 最佳实践

1. **明确需求**：根据业务需求选择合适的策略和选项
2. **测试验证**：使用提供的测试工具验证合并结果
3. **性能测试**：对大量数据进行性能测试
4. **文档记录**：在代码中记录使用的策略和原因

## 注意事项

- `preserveNonEmptyValues` 选项会影响所有合并策略的行为
- 空值判断基于 JavaScript 的真值/假值概念，但更加严格
- 建议在生产环境使用前进行充分测试

这个功能让 `NestedDataFlattener` 在处理真实业务数据时更加智能和可靠！
