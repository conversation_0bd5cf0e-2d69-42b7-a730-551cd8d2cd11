<template>
  <div v-loading="isLock">
    <!-- 基础信息 -->
    <base-info-detail
      ref="baseInfo"
      :type="type"
      v-bind="$attrs"
    ></base-info-detail>
    <!-- 合同信息 -->
    <contract-info
      ref="contractInfo"
      :type="type"
      v-bind="$attrs"
    ></contract-info>
    <!-- 客户信息 -->
    <customer-info
      ref="customerInfo"
      :type="type"
      v-bind="$attrs"
      v-on="$listeners"
    ></customer-info>
    <!-- 换车信息 -->
    <transfer-car-info
      ref="transferCarInfo"
      v-bind="$attrs"
    ></transfer-car-info>
    <!-- 替换车信息 -->
    <replace-car-info ref="replaceCarInfo" v-bind="$attrs"></replace-car-info>
    <!-- 替换车转租赁信息 -->
    <transfer-lease-car-info
      ref="transferLeaseCarInfo"
      v-bind="$attrs"
    ></transfer-lease-car-info>
    <!-- 业务人员操作日志 -->
    <!-- <seller-log ref="log" :type="type" v-bind="$attrs" /> -->
    <!-- 商户合作信息 -->
    <merchant-info ref="merchantInfo" :type="type" v-bind="$attrs" />
    <!-- 管理备注 -->
    <management-note
      :type="type"
      v-bind="$attrs"
      v-on="$listeners"
    ></management-note>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import BaseInfoDetail from '../components/BaseInfo.vue'
import CustomerInfo from '@/views/common/parentOrder/components/customerInfo'
import orderMixins from '@/views/common/common/mixins/orderLink'
import ReplaceCarInfo from '../components/ReplaceCarInfo.vue'
import ManagementNote from '@/views/common/parentOrder/components/managementNote'
import contractInfo from '@/views/common/parentOrder/components/contractInfo'
// import SellerLog from '@/views/common/parentOrder/components/sellerLog'
import MerchantInfo from '@/views/common/parentOrder/components/merchantInfo'
import TransferCarInfo from '../components/TransferCarInfo.vue'
import TransferLeaseCarInfo from '../components/TransferLeaseCarInfo.vue'
export default {
  name: 'BaseInfo',
  components: {
    BaseInfoDetail,
    CustomerInfo,
    ReplaceCarInfo,
    ManagementNote,
    contractInfo,
    // SellerLog,
    MerchantInfo,
    TransferCarInfo,
    TransferLeaseCarInfo,
  },
  mixins: [orderMixins],
  props: {
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type: {
      type: [Number, String],
      default: '1',
    },
  },
  data() {
    return {
      // loading加载
      isLock: false,
    }
  },
  methods: {
    /**
     * @description:获取页面详情数据
     * @param {type}
     * @return:
     */
    refresh() {
      this.$nextTick(async () => {
        try {
          this.$refs.replaceCarInfo &&
            (await this.$refs.replaceCarInfo.getTableData())
          this.$refs.transferCarInfo &&
            (await this.$refs.transferCarInfo.init())
          this.$refs.log && (await this.$refs.log.refresh())
          this.isLock = false
        } catch (error) {
          this.isLock = false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
