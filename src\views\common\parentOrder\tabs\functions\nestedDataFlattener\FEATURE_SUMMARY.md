# NestedDataFlattener 父子优先级控制功能总结

## 功能概述

为 `NestedDataFlattener` 类添加了完整的父子优先级控制功能，支持在父子项存在相同字段时，灵活控制使用哪个值。

## 新增功能

### 1. 优先级策略支持

- **子项优先 (child-first)**：默认策略，保持子项值，父项仅填充缺失字段
- **父项优先 (parent-first)**：父项值覆盖子项相同字段
- **智能合并 (merge)**：字段级别的精确优先级控制

### 2. 新增配置选项

```javascript
{
  mergeStrategy: 'child-first' | 'parent-first' | 'merge',
  parentPriorityFields: [], // 父项优先的字段列表
  childPriorityFields: [],  // 子项优先的字段列表
  customMergeStrategy: null // 自定义合并策略函数
}
```

### 3. 新增方法

```javascript
// 链式调用设置优先级策略
flattener.setPriorityStrategy(strategy, options)
```

### 4. 增强的合并逻辑

- 支持字段级别的优先级控制
- 智能数据类型合并（数组、对象、字符串）
- 自定义合并策略支持

## 文件修改

### 核心文件

1. **utils.js** - 主要类文件

   - 添加 `setPriorityStrategy()` 方法
   - 重构 `_mergeItems()` 方法
   - 新增 `_enhancedMerge()` 和 `_smartMerge()` 方法
   - 新增 `_mergeFieldValue()` 方法

2. **functions.js** - 工具函数文件

   - 增强 `mergeParentChild()` 函数
   - 支持新的优先级字段参数

3. **data.js** - 配置文件
   - 添加新的默认配置选项

### 文档和示例文件

4. **PRIORITY_GUIDE.md** - 详细使用指南
5. **priority-example.js** - 使用示例代码
6. **priority-test.js** - 功能测试代码
7. **usage-example.vue** - Vue 组件使用示例
8. **FEATURE_SUMMARY.md** - 功能总结（本文件）

## 使用示例

### 基础使用

```javascript
// 子项优先（默认）
const flattener1 = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority'],
  mergeStrategy: 'child-first',
})

// 父项优先
const flattener2 = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority'],
  mergeStrategy: 'parent-first',
})

// 智能合并
const flattener3 = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority'],
  mergeStrategy: 'merge',
  parentPriorityFields: ['priority'],
  childPriorityFields: ['status'],
})
```

### 链式调用

```javascript
const result = new NestedDataFlattener({
  parentFields: ['name', 'status', 'priority'],
})
  .setPriorityStrategy('merge', {
    parentPriorityFields: ['priority'],
    childPriorityFields: ['status'],
  })
  .flatten(data)
```

### 自定义策略

```javascript
const flattener = new NestedDataFlattener({
  parentFields: ['name', 'status', 'amount'],
  customMergeStrategy: (child, parent, options) => {
    const result = { ...child }

    // 自定义合并逻辑
    if (parent.amount && child.amount) {
      result.totalAmount = parent.amount
      result.itemAmount = child.amount
    }

    return result
  },
})
```

## 测试验证

所有功能都通过了完整的测试验证：

```bash
node src/views/common/parentOrder/tabs/functions/priority-test.js
```

测试结果：

```
开始运行 NestedDataFlattener 父子优先级控制测试...

测试子项优先策略...
✓ 子项优先策略测试通过

测试父项优先策略...
✓ 父项优先策略测试通过

测试智能合并策略...
✓ 智能合并策略测试通过

测试链式调用配置...
✓ 链式调用配置测试通过

测试自定义合并策略...
✓ 自定义合并策略测试通过

🎉 所有测试通过！
```

## 向后兼容性

- 所有现有功能保持不变
- 默认行为保持 `child-first` 策略
- 现有配置选项继续有效
- 原有的 `customMerge` 函数仍然支持

## 应用场景

1. **订单系统**：订单状态子项优先，优先级父项优先
2. **商品分类**：分类信息父项优先，商品详情子项优先
3. **权限管理**：权限设置父项优先，个人设置子项优先
4. **数据同步**：根据数据来源设置不同优先级

## 性能考虑

- 新增功能对性能影响最小
- 智能合并策略略有性能开销，但在可接受范围内
- 自定义策略性能取决于用户实现
- 建议对大量数据使用简单策略

## 总结

这次功能增强为 `NestedDataFlattener` 类提供了完整的父子优先级控制能力，满足了各种复杂的数据合并需求，同时保持了良好的向后兼容性和易用性。
