<template>
  <div class="dst-add-card-wrap dstform--demo-wrap" v-loading="isLock">
    <div class="main-box">
      <!-- 基本信息 -->
      <base-info v-if="baseInfoForm.orderCode" :base-info-form="baseInfoForm" :type="type"></base-info>
      <!-- 商品信息 -->
      <goods-info ref="goodsInfo" :type="type" :base-info-form="baseInfoForm" :code="orderCode"></goods-info>
      <!-- 费用信息 -->
      <fee-info
        ref="feeInfo"
        :type="type"
        :base-info-form="baseInfoForm"
        :id="orderId"
        :code="orderCode"
        @refresh="refresh"
      ></fee-info>
      <!-- 客户信息 -->
      <customer-info v-if="!isLock" :base-info-form="baseInfoForm"></customer-info>
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { getRepayMarginOrderBasic } from '@/api/consignmentRental/repayMarginOrders'
import { BusEmit } from '@/mixins/busEvent'
import BaseInfo from './components/BaseInfo'
import rentalComponents from '@/views/common/childOrders/detailTableList'
const { GoodsInfo, GiveInfo, FeeInfo, CustomerInfo } = rentalComponents
const components = {
  BaseInfo,
  GoodsInfo,
  CustomerInfo,
  GiveInfo,
  FeeInfo,
}
export default {
  name: 'OldConsignmentRentalRepayMarginOrderDetail',
  components,
  data() {
    return {
      // loading加载
      isLock: false,
      // 基本信息详情展示
      baseInfoForm: {},
    }
  },
  computed: {
    // 子订单id
    orderId() {
      return this.$route.query.id
    },
    // 子订单code
    orderCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return (this.orderCode && Number(this.orderCode.slice(0, 2))) || 1
    },
  },
  created() {
    this.getDetailData()
  },
  methods: {
    /**
     * @description:获取页面详情数据
     * @param {type}
     * @return:
     */
    getDetailData() {
      this.isLock = true
      getRepayMarginOrderBasic({ orderCode: this.orderCode })
        .then((res) => {
          this.baseInfoForm = res.data
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:刷新页面数据
     * @param {string} type 区分费用模块和抵扣模块
     * @return:
     */
    refresh() {
      this.$nextTick(() => {
        this.getDetailData()
        Object.values(components).forEach((element) => {
          BusEmit(element.name, 'refresh')
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
