/**
 * @description:推荐列表配置
 * @param {type}
 * @return:
 */
export function recommendConfig() {
  return [
    { label: '真实姓名', prop: 'saleName', minWidth: 170 },
    { label: '推荐码', prop: 'inviteCode', minWidth: 170 },
    {
      label: '手机号',
      prop: 'phone',
      minWidth: 170,
      dataPrivacy: { fileBusinessType: 1 },
    },
    {
      label: '用户ID',
      prop: 'userId',
      minWidth: 200,
      type: 'link',
      click: ({ row }) => {
        this.getCustomerInfo(row.userId)
      },
    },
  ]
}

/**
 * @description:业务归属列表配置
 * @param {type}
 * @return:
 */
export function salemanConfig() {
  return [
    { label: '工号', prop: 'jobNum', minWidth: 170 },
    { label: '真实姓名', prop: 'saleName', minWidth: 170 },
    {
      label: '手机号',
      prop: 'phone',
      minWidth: 170,
      dataPrivacy: { fileBusinessType: 1 },
    },
    {
      label: '用户ID',
      prop: 'userId',
      minWidth: 200,
      type: 'link',
      click: ({ row }) => {
        this.getCustomerInfo(row.userId)
      },
    },
    {
      label: '角色',
      prop: 'typeName',
      minWidth: 200,
    },
  ]
}
