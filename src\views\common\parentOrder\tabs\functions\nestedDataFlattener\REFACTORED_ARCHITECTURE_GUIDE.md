# NestedDataFlattener 重构架构指南

## 🎯 重构概述

根据您的建议，我们对 `NestedDataFlattener` 进行了重要的架构重构：

### 1. 预设配置独立化 ✅
- 将预设配置从类内部移出，创建独立的 `presets.js` 文件
- 提供 `PresetManager` 类来管理预设配置
- 支持动态注册、扩展和管理预设配置

### 2. 合并策略架构重构 ✅
- **基础策略**：`child-first`、`parent-first`、`merge`
- **智能覆盖**：作为独立选项 `smartOverride`，可与任何基础策略组合使用
- 移除了 `smart-override` 作为独立策略，改为配置选项

## 🏗️ 新架构详解

### 预设配置管理

#### 独立的预设配置文件
```javascript
// presets.js
export const PRESET_CONFIGS = {
  default: { ... },
  orderAdjustment: { ... },
  simpleParentChild: { ... },
  // 更多预设...
}

export class PresetManager {
  static register(name, config) { ... }
  static get(name) { ... }
  static extend(baseName, newName, extensions) { ... }
}
```

#### 使用预设配置
```javascript
// 使用预设
const flattener = NestedDataFlattener.create('orderAdjustment')

// 扩展预设
PresetManager.extend('orderAdjustment', 'myCustomAdjustment', {
  smartOverride: true,
  customFields: { source: 'custom' }
})

// 注册新预设
PresetManager.register('myPreset', {
  childrenKey: 'items',
  mergeStrategy: 'parent-first',
  smartOverride: true
})
```

### 合并策略新架构

#### 基础策略 + 智能覆盖组合
```javascript
// 旧方式（已废弃）
.setPriorityStrategy('smart-override')

// 新方式：基础策略 + 智能覆盖
.setPriorityStrategy('child-first', { smartOverride: true })
.setPriorityStrategy('parent-first', { smartOverride: true })
.setPriorityStrategy('merge', { smartOverride: true })

// 或者使用专门的方法
.setPriorityStrategy('child-first')
.enableSmartOverride(true)
```

#### 配置选项说明
```javascript
{
  // 基础合并策略
  mergeStrategy: 'child-first', // child-first | parent-first | merge
  
  // 智能覆盖选项（独立于基础策略）
  smartOverride: false,         // 是否启用智能覆盖
  preserveNonEmptyValues: false, // 是否保留非空值
  
  // 字段级控制（仅在 merge 策略下生效）
  parentPriorityFields: [],     // 父项优先字段
  childPriorityFields: [],      // 子项优先字段
}
```

## 🚀 使用示例

### 1. 基础用法
```javascript
// 使用预设配置
const result = NestedDataFlattener
  .create('orderAdjustment')
  .flatten(data)

// 自定义配置
const result = NestedDataFlattener
  .create({
    childrenKey: 'items',
    mergeStrategy: 'parent-first',
    smartOverride: true
  })
  .flatten(data)
```

### 2. 策略组合使用
```javascript
const result = NestedDataFlattener
  .create('default')
  // 设置基础策略为子项优先
  .setPriorityStrategy('child-first')
  // 启用智能覆盖，避免空值覆盖有值字段
  .enableSmartOverride(true)
  .flatten(data)
```

### 3. 复杂场景配置
```javascript
const result = NestedDataFlattener
  .create('orderAdjustment')
  // 使用字段级精确控制
  .setPriorityStrategy('merge', {
    parentPriorityFields: ['adjustCode', 'parentOrderCode'],
    childPriorityFields: ['itemName', 'adjustAmount'],
    smartOverride: true,
    preserveNonEmptyValues: true
  })
  // 添加自定义字段
  .addCustomFields({
    processedAt: new Date(),
    source: 'system'
  })
  .flatten(data)
```

### 4. 预设配置扩展
```javascript
// 基于现有预设创建新预设
PresetManager.extend('orderAdjustment', 'enhancedOrderAdjustment', {
  smartOverride: true,
  customFields: {
    enhanced: true,
    version: '2.0'
  }
})

// 使用扩展后的预设
const result = NestedDataFlattener
  .create('enhancedOrderAdjustment')
  .flatten(data)
```

## 🔄 迁移指南

### 从旧版本迁移

#### 旧方式
```javascript
// 旧的 smart-override 策略
.setPriorityStrategy('smart-override')
```

#### 新方式
```javascript
// 新的组合方式
.setPriorityStrategy('child-first', { smartOverride: true })
// 或
.setPriorityStrategy('child-first')
.enableSmartOverride(true)
```

### 向后兼容性
- 旧的 `smart-override` 策略会自动转换为 `child-first + smartOverride`
- 现有代码无需修改，会自动适配新架构
- 控制台会显示迁移提示信息

## 🎯 架构优势

### 1. 更清晰的职责分离
- **基础策略**：负责基本的合并逻辑
- **智能覆盖**：负责空值处理逻辑
- **预设配置**：负责场景化配置管理

### 2. 更灵活的组合方式
```javascript
// 任何基础策略都可以启用智能覆盖
'child-first' + smartOverride
'parent-first' + smartOverride  
'merge' + smartOverride
```

### 3. 更好的可维护性
- 预设配置独立管理，易于维护和扩展
- 策略逻辑清晰分离，便于理解和调试
- 支持动态配置注册和扩展

### 4. 更强的扩展性
- 可以轻松添加新的基础策略
- 可以独立扩展智能覆盖逻辑
- 支持插件化的预设配置管理

## 📊 在 OrderAdjustment.vue 中的应用

```javascript
// 当前使用（自动适配新架构）
this.tableList = NestedDataFlattener
  .create('orderAdjustment') // 使用独立的预设配置
  .flatten(orderAdjustData)

// 高级用法示例
this.tableList = NestedDataFlattener
  .create('orderAdjustment')
  // 确保启用智能覆盖（预设中已配置）
  .enableSmartOverride(true)
  // 添加自定义字段
  .addCustomFields({
    processedBy: this.$store.state.user?.name,
    processedAt: new Date().toISOString()
  })
  .flatten(orderAdjustData)
```

## 🎉 总结

重构后的架构具有以下特点：

✅ **预设配置独立化** - 便于维护和扩展  
✅ **策略职责清晰** - 基础策略 + 智能覆盖的组合模式  
✅ **向后兼容** - 现有代码无需修改  
✅ **灵活组合** - 任意基础策略都可配合智能覆盖使用  
✅ **易于扩展** - 支持动态配置管理和插件化扩展  

这个新架构更符合单一职责原则，提供了更好的灵活性和可维护性！
