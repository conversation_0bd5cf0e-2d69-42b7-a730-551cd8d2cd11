/**
 * @description:赠送信息表单配置
 * @param {type}
 * @return:
 */
export function giveConfig() {
  const prop = {
    0: {
      prop: 'benefitRecordId',
      type: 'slot',
    },
    2: {
      prop: 'releaseRecordId',
      type: 'text',
    },
  }
  const { propName, type } = prop[this.type]
  return [
    {
      prop: 'releaseRecordId',
      label: '发放记录ID',
      type: type,
      class:
        propName && this.baseInfoForm[propName]
          ? 'dst-green-underline'
          : 'none-data',
      isTooltip: true,
      click: () => {
        if (propName && this.baseInfoForm[propName]) {
          this.$linkTo({
            model: 'cec',
            path: '/goodsCenter/equityCard/recordDetail',
            query: {
              id: this.baseInfoForm[propName],
            },
          })
        }
      },
    },
  ]
}
