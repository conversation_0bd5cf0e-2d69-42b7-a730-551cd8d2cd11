<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate
      ref="dst-filtrate"
      :data-arr="setFiltrateData"
      :form-value="searchValue"
      @searchClick="searchClick"
    ></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="car_rental_father_orders"
        row-key="orderCode"
        is-check-box
        :ref="isClearSelected"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :toolbar-config="toolbarConfig"
        @pageChange="pageChange"
        @selection-change="handleSelectionChange"
      >
        <sys-button
          v-if="btnList.some((item) => !!item.show)"
          slot="toolbar_buttons"
          :btn-list="btnList"
          :limit="14"
        ></sys-button>
      </sys-grid>
    </div>
    <hand-car
      v-if="handCarFlag"
      :visible.sync="handCarFlag"
      :list="selectList"
      :customer-info="customerInfo"
      :type="type"
      @refresh="resetRefresh"
    />
    <return-car
      v-if="returnCarFlag"
      :visible.sync="returnCarFlag"
      :list="selectList"
      :type="type"
      :station-list="stationList"
      @refresh="resetRefresh"
    />
    <modify-sales-belong
      v-if="saleBelongFlag"
      :visible.sync="saleBelongFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="resetRefresh"
    />
    <confirm-collect
      v-if="confirmCollectFlag"
      :visible.sync="confirmCollectFlag"
      :list="selectOrderCodes"
      :type="type"
      :mode="confirmCollectType"
      @refresh="resetRefresh"
    />
    <invalid-dialog
      v-if="invaildFlag"
      :visible.sync="invaildFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="resetRefresh"
    />
    <station-dialog
      v-if="modifyStationFlag"
      :visible.sync="modifyStationFlag"
      :current-data="selectList[0]"
      :type="type"
      @refresh="resetRefresh"
    />
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { btnList } from '@/views/common/parentOrder/dialog/list/functions/data.js'
import { setTableHeadArr } from './functions/table'
import mixin from '@/mixins/table.js'
import { getParentOrderList } from '@/api/common/rental/parent'
import { changeVariableName, clearObjItem } from '@/utils/business'
import mutltipOptions from '@/views/common/parentOrder/dialog/list/mixins/mutltipOptions.js'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import selectShoper from '@/views/common/common/mixins/selectShoper.js'
import { isEmpty } from '@/utils'
import marketOrgMixins from '@/views/common/common/mixins/marketOrg.js'
export default {
  name: 'carRentalFatherOrders',
  components: {
    HandCar: () => import('@/views/common/parentOrder/dialog/list/HandCar.vue'),
    ReturnCar: () =>
      import('@/views/common/parentOrder/dialog/list/RefundCar.vue'),
    ModifySalesBelong: () =>
      import('@/views/common/parentOrder/dialog/list/ModifySaleBelong.vue'),
    ConfirmCollect: () =>
      import('@/views/common/parentOrder/dialog/list/ConfirmCollect.vue'),
    InvalidDialog: () =>
      import('@/views/common/parentOrder/dialog/list/InvalidDialog.vue'),
    StationDialog: () =>
      import('@/views/common/parentOrder/dialog/list/StationDialog.vue')
  },
  mixins: [
    mixin,
    mutltipOptions,
    selectCustomer,
    selectShoper,
    marketOrgMixins
  ],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 父订单总数
      paginationTotal: 0,
      // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
      type: 0,
      // 当前选中的父订单数据
      selectList: [],
      // 是否自动清除复选项
      isClearSelected: 'sysTable',
      // 工具栏配置
      toolbarConfig: {
        slots: { buttons: 'toolbar_buttons' }
      }
    }
  },
  computed: {
    // 父订单搜索配置项
    setFiltrateData,
    // 父订单列表表头项
    setTableHeadArr,
    // 父订单列表按钮配置
    btnList
  },
  methods: {
    /**
     * @description:请求父订单列表数据
     * @param {type}
     * @return:
     */
    getTableData() {
      if (this.initFlag) {
        this.isLock = true
        const data = this.getParams()
        getParentOrderList(data, this.type)
          .then(res => {
            this.paginationTotal = res.data.totalNum || 0
            this.tableList = res.data.list || []
            this.initSourceStatus()
          })
          .finally(() => {
            this.isLock = false
          })
      }
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = _.cloneDeep(this.searchData)
      changeVariableName(params, 'createTime', true, 'Time')
      changeVariableName(params, 'realDeliveryTime', true, 'Time')
      changeVariableName(params, 'realBackCarTime', true, 'Time')
      const expLeaseMonth = this.getMouthData(params, 'expLeaseMonth')
      params.expLeaseStartMonth = expLeaseMonth.start
      params.expLeaseEndMonth = expLeaseMonth.end
      const realLeaseMonth = this.getMouthData(params, 'realLeaseMonth')
      params.realLeaseStartMonth = realLeaseMonth.start
      params.realLeaseEndMonth = realLeaseMonth.end
      if (params.regionIds && params.regionIds.length) {
        params.serviceOrgCityCode =
          params.regionIds[params.regionIds.length - 1]
      }
      const { orderCodeBatch, carNoBatch, marketingOrgCodeItems } = params
      if (orderCodeBatch) {
        params.orderCodes = orderCodeBatch.split(',')
        delete params.orderCodeBatch
      }
      if (carNoBatch) params.carNoList = carNoBatch.split(',')
      delete params.regionIds
      if (isEmpty(params.customerIds)) {
        params.customerIds = []
      }
      if (!isEmpty(params.marketingOrgCodeItems)) {
        params.marketingOrgCodeList = [
          marketingOrgCodeItems[marketingOrgCodeItems.length - 1]
        ]
      }
      delete params.marketingOrgCodeItems
      this.getCustomerIds(params, true)
      const result = clearObjItem(params)
      if (!_.isEqual(this.searchParams, result)) {
        this.searchParams = _.cloneDeep(result)
      }
      return {
        ...result,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
    },
    /**
     * @description:拆分查询项月份数据
     * @param {object} data 当前的数据集合
     * @param {key} string 所对应的字段名称
     * @return:
     */
    getMouthData(data, key) {
      if (data[key]) {
        const arr = data[key].split('-')
        const pararms = {
          start: this.dealData(arr[0]),
          end: this.dealData(arr[1])
        }
        delete data[key]
        return pararms
      } else {
        delete data[key]
        return {
          start: '',
          end: ''
        }
      }
    },
    /**
     * @description:将数据格式化处理
     * @param {string} data 当前需要格式化的字符串
     * @return:
     */
    dealData(data) {
      return data !== '0' ? (data ? Number(data) : data) : null
    },
    /**
     * @description:跳转详情页面
     * @param {type}
     * @return:
     */
    goDetail(row) {
      this.$router.push({
        path: '/carRental/fatherOrders/detail',
        query: {
          id: row.id,
          name: 'BaseInfo',
          code: row.orderCode
        }
      })
    },
    /**
     * @description:列表选中触发
     * @param {array} list 当前选中的列表数据
     * @return:
     */
    handleSelectionChange(list) {
      this.selectList = _.cloneDeep(list)
    },
    /**
     * @description: 重置数据集合
     */
    clickReset() {
      this.customerIds = []
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/views/common/common/styles/table.scss';
</style>
