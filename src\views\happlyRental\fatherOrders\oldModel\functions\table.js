import { showData } from '@/utils/business'

/**
 * @description:父订单列表数据
 * @param {type}
 * @return:
 */
export function setTableHeadArr() {
  return [
    { label: '父订单编码', prop: 'orderCode', minWidth: 220 },
    { label: '订单状态', prop: 'carStatusName', minWidth: 150 },
    { label: '客户编码', prop: 'customerId', minWidth: 180 },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '合同编码', prop: 'contractCodes', minWidth: 300 },
    { label: '合同乙方', prop: 'contractPartyName', minWidth: 220 },
    // { label: '服务组织', prop: 'serviceOrgCityName', minWidth: 170 },
    { label: '车牌号', prop: 'carNo', minWidth: 120 },
    { label: '车架号', prop: 'vinCode', minWidth: 210 },
    { label: '车辆用途', prop: 'carUseName', minWidth: 170 },
    { label: '预计交车时间', prop: 'expDeliveryTime', minWidth: 180 },
    // { label: '预计履约结束日期', prop: 'expBackCarTime', minWidth: 180 },
    { label: '实际交车时间', prop: 'realDeliveryTime', minWidth: 180 },
    { label: '实际还车时间', prop: 'realBackCarTime', minWidth: 180 },
    { label: '下单渠道', prop: 'orderSourceName', minWidth: 150 },
    { label: '订单渠道', prop: 'orderChannel', minWidth: 150 },
    { label: '保证金', prop: 'isBondName', minWidth: 120 },
    { label: '预计租赁时长（月）', prop: 'expLeaseMonth', minWidth: 150 },
    { label: '实际租赁时长（月）', prop: 'realLeaseMonth', minWidth: 150 },
    {
      prop: 'expireTime',
      label: '订单有效期',
      minWidth: 300,
      render: (h, scope) => {
        return (
          <span>
            {showData({
              data: [scope.row.expireStartTime, scope.row.expireEndTime],
              showEmpty: true,
              spliter: '~',
              type: 'time',
              strict: false,
            })}
          </span>
        )
      },
    },
    // {
    //   prop: 'performTime',
    //   label: '订单履约时间',
    //   minWidth: 300,
    //   render: (h, scope) => {
    //     return (
    //       <span>
    //         {showData({
    //           data: [scope.row.performStartTime, scope.row.performEndTime],
    //           showEmpty: true,
    //           spliter: '~',
    //           type: 'time',
    //           strict: false,
    //         })}
    //       </span>
    //     )
    //   },
    // },
    { label: '创建时间', prop: 'createTime', minWidth: 180 },
    { label: '服务组织', prop: 'serviceOrgCityName', minWidth: 150 },
    { label: '营销组织', prop: 'marketingOrgName', minWidth: 150 },
    { label: '业务通路', prop: 'customerGroupTypeName', minWidth: 170 },
    { label: '原欢乐租订单号', prop: 'originalParentCode', minWidth: 200 },
    { label: '业务类型', prop: 'businessTypeName', minWidth: 150 },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '详情',
          click: ({ row }) => this.goDetail(row),
        },
      ],
    },
  ]
}
