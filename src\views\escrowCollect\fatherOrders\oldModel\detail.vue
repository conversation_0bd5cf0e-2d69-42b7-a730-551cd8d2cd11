<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <el-tabs class="dst-card-tabs" v-model="activeName" @tab-click="changeTab">
      <el-tab-pane v-for="item in tabList" :key="item.name" :label="item.label" :name="item.name">
        <component
          v-if="activeName === item.name && baseInfoForm"
          :is-new="false"
          :base-info-form.sync="baseInfoForm"
          :is="activeName"
          :ref="activeName"
          :type="type"
          :parent-id="parentId"
          :parent-code="parentCode"
          :customer-info-form="customerInfoForm"
          @parentRefresh="refresh"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import BaseInfo from './tabs/BaseInfo'
import commonTabs from '@/views/common/parentOrder/tabs'
import {
  getParentOrderBasic,
  getParentOrderCustomer
} from '@/api/common/rental/parent'
export default {
  name: 'OldEscrowCollectFatherOrderDetail',
  components: { BaseInfo, ...commonTabs },
  data() {
    return {
      // 加载对应的组件
      activeName: '',
      // 对应tabs的信息
      tabList: [
        {
          name: 'BaseInfo',
          label: '基础信息'
        },
        {
          name: 'EquityPrefer',
          label: '权益/优惠'
        },
        {
          name: 'AgreeModule',
          label: '履约信息'
        },
        {
          name: 'ChildOrders',
          label: '子订单信息'
        },
        {
          name: 'OrderLog',
          label: '订单日志'
        }
      ],
      // 是否需要loading
      isLock: false,
      // 获取父订单详情数据
      baseInfoForm: null,
      // 获取父订单客户数据
      customerInfoForm: {},
      // 获取销售归属数据
      parentOrderSalemanList: []
    }
  },
  computed: {
    // 获取当前父订单id
    parentId() {
      return this.$route.query.id
    },
    // 获取当前父订单code
    parentCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return this.$route.query.type || 5
    }
  },
  created() {
    if (this.$route.query.name) {
      this.activeName = this.$route.query.name
    } else {
      this.activeName = 'BaseInfo'
    }
    this.tabList = this.isShowPortalLog()
      ? this.tabList
      : this.tabList.filter(item => !item.label.includes('日志'))
    this.init()
  },
  methods: {
    /**
     * @description:切换tab栏触发
     * @param {type}
     * @return:
     */
    changeTab() {
      this.$router.push({
        path: '/escrowCollect/fatherOrders/detail',
        query: {
          id: this.parentId,
          name: this.activeName,
          code: this.parentCode,
          type: this.type
        }
      })
    },
    /**
     * @description:刷新数据
     * @param {type}
     * @return:
     */
    refresh() {
      this.$nextTick(() => {
        this.init()
        this.$refs[this.activeName] &&
          typeof this.$refs[this.activeName][0].refresh === 'function' &&
          this.$refs[this.activeName][0].refresh()
      })
    },
    /**
     * @description:初始化基础信息数据
     * @param {type}
     * @return:
     */
    async init() {
      this.isLock = true
      await Promise.all([
        getParentOrderBasic({ orderCode: this.parentCode }, this.type),
        getParentOrderCustomer({ parentOrderCode: this.parentCode }, this.type)
      ])
        .then(res => {
          let baseInfoForm = res[0].data.parentOrderBasic || {}
          baseInfoForm.carBrandType = res[0].data.parentOrderSalemanList || []
          const [data] = baseInfoForm.carBrandType
          baseInfoForm.jobNum = data ? data.jobNum || '' : ''
          baseInfoForm.saleName = data ? data.saleName || '' : ''
          baseInfoForm.showSaleName =
            baseInfoForm.saleName +
            (baseInfoForm.jobNum ? '/' + baseInfoForm.jobNum : '')
          this.customerInfoForm = res[1].data || {}
          this.baseInfoForm = _.cloneDeep(baseInfoForm)
        })
        .finally(() => {
          this.isLock = false
        })
    }
  }
}
</script>
