<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <el-tabs
      class="dst-card-tabs"
      :class="{ 'dst-card-new-tabs': !isShowBtn }"
      v-model="activeName"
      @tab-click="changeTab"
    >
      <el-tab-pane v-for="item in tabList" :key="item.name" :label="item.label" :name="item.name">
        <component
          v-if="activeName === item.name && baseInfoForm"
          :base-info-form.sync="baseInfoForm"
          :is="activeName"
          :ref="activeName"
          :type="type"
          :parent-id="parentId"
          :parent-code="parentCode"
          :customer-info-form="customerInfoForm"
          @parentRefresh="refresh"
        ></component>
      </el-tab-pane>
    </el-tabs>
    <div v-if="isShowBtn" class="dst-add-bottom-btn-box">
      <sys-button :btn-list="btnList" :limit="14" fold flex></sys-button>
    </div>
    <template v-if="componentKey">
      <component
        :is="componentKey"
        :ref="componentKey"
        :parent-code="parentCode"
        :parent-id="parentId"
        :type="type"
        :component-key.sync="componentKey"
        :customer-info-form="customerInfoForm"
        @refresh="refresh"
      ></component>
    </template>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
/* eslint-disable indent */
import btnMixin from './mixins/btnMixin'
import BaseInfo from './tabs/BaseInfo'
import commonDialog from '@/views/common/parentOrder/dialog/detail'
import commonTabs from '@/views/common/parentOrder/tabs'
import {
  getParentOrderBasic,
  getParentOrderCustomer
} from '@/api/common/rental/parent'
// import { getOrgByCode } from '@/api/common'
export default {
  name: 'OldHapplyRentalFatherOrderDetail',
  components: {
    BaseInfo,
    ...commonDialog,
    ...commonTabs
  },
  mixins: [btnMixin],
  data() {
    return {
      // 加载对应的组件
      activeName: '',
      // 对应tabs的信息
      tabList: [
        {
          name: 'BaseInfo',
          label: '基础信息'
        },
        {
          name: 'EquityPrefer',
          label: '权益/优惠'
        },
        {
          name: 'AgreeInfo',
          label: '交还车信息'
        },
        {
          name: 'AgreeModule',
          label: '履约信息'
        },
        {
          name: 'ChildOrders',
          label: '子订单信息'
        },
        {
          name: 'OrderLog',
          label: '订单日志'
        }
      ],
      // 是否需要loading
      isLock: false,
      // 弹框组件key
      componentKey: '',
      // 获取父订单详情数据
      baseInfoForm: null,
      // 获取父订单客户数据
      customerInfoForm: {}
    }
  },
  computed: {
    // 获取当前父订单id
    parentId() {
      return this.$route.query.id
    },
    // 获取当前父订单code
    parentCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return (this.parentCode && Number(this.parentCode.slice(0, 2))) || 4
    },
    // 判断是否存在按钮显示
    isShowBtn() {
      return this.btnList.some(item => !!item.show)
    }
  },
  created() {
    if (this.$route.query.name) {
      this.activeName = this.$route.query.name
    } else {
      this.activeName = 'BaseInfo'
    }
    this.tabList = this.isShowPortalLog()
      ? this.tabList
      : this.tabList.filter(item => !item.label.includes('日志'))
    this.init()
  },
  methods: {
    /**
     * @description:切换tab栏触发
     * @param {type}
     * @return:
     */
    changeTab() {
      this.$router.push({
        path: '/happlyRental/fatherOrders/detail',
        query: {
          id: this.parentId,
          name: this.activeName,
          code: this.parentCode,
          type: this.type
        }
      })
    },
    /**
     * @description:刷新数据
     * @param {type}
     * @return:
     */
    refresh() {
      this.$nextTick(() => {
        this.init()
        this.$refs[this.activeName] &&
          typeof this.$refs[this.activeName][0].refresh === 'function' &&
          this.$refs[this.activeName][0].refresh()
      })
    },
    /**
     * @description:初始化基础信息数据
     * @param {type}
     * @return:
     */
    async init() {
      this.isLock = true
      await Promise.all([
        getParentOrderBasic({ parentOrderCode: this.parentCode }, this.type),
        getParentOrderCustomer({ parentOrderCode: this.parentCode }, this.type)
      ])
        .then(res => {
          const baseInfoForm = res[0].data || {}
          const parentOrderExtend = baseInfoForm.parentOrderExtend
          this.customerInfoForm = res[1].data || {}
          baseInfoForm.ddPicture = [
            {
              fileName: '创单审批截图',
              accessUrl: baseInfoForm.ddPicture,
              fileUrl: baseInfoForm.ddPicture
            }
          ]
          baseInfoForm.refundApprovalPicture =
            parentOrderExtend && parentOrderExtend.refundApprovalPicture
              ? [
                  {
                    fileName: '退款审批截图',
                    accessUrl: parentOrderExtend.refundApprovalPicture,
                    fileUrl: parentOrderExtend.refundApprovalPicture
                  }
                ]
              : []
          // if (baseInfoForm.marketingOrgCode) {
          //   getOrgByCode({ code: baseInfoForm.marketingOrgCode })
          //     .then((result) => {
          //       baseInfoForm.marketingOrgName = result.data.name || ''
          //       this.baseInfoForm = _.cloneDeep(baseInfoForm)
          //     })
          //     .catch(() => {
          //       baseInfoForm.marketingOrgName = ''
          //       this.baseInfoForm = _.cloneDeep(baseInfoForm)
          //     })
          //     .finally(() => {
          //       this.isLock = false
          //     })
          // } else {
          //   this.baseInfoForm = _.cloneDeep(baseInfoForm)
          //   this.isLock = false
          // }
          baseInfoForm.contractCode = baseInfoForm.contractCodes
          baseInfoForm.parentOrderCode = baseInfoForm.orderCode
          this.baseInfoForm = _.cloneDeep(baseInfoForm)
        })
        .finally(() => {
          this.isLock = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .dst-card-tabs {
  height: calc(100% - 60px) !important;
  &.dst-card-new-tabs {
    height: 100% !important;
  }
}
</style>
