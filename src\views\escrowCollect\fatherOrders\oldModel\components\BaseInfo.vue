<template>
  <sys-card title="基础信息" slot-name="baseInfo" v-loading="isLock">
    <dstForm
      ref="ruleForm"
      slot="baseInfo_body"
      label-width="140px"
      mode-type="detail"
      v-model="baseInfoForm"
      :data-arr="baseInfoConfig"
    >
    </dstForm>
  </sys-card>
</template>

<script>
/* eslint-disable jsx-quotes */
import { baseInfo1, baseInfo2 } from '../functions/detail-data'
import contractMixins from '@/views/common/common/mixins/contractMixins.js'
import RecommendInfo from '@/views/common/parentOrder/components/baseInfo/RecommendInfo.vue'
import SalemanInfo from '@/views/common/parentOrder/components/baseInfo/SalemanInfo.vue'
import { isEmpty } from '@/utils'
export default {
  name: 'BaseInfoDetail',
  components: { RecommendInfo, SalemanInfo },
  mixins: [contractMixins],
  props: {
    // 父订单基础信息
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  computed: {
    // 基础信息展示项处理
    baseInfoConfig() {
      const baseData1 = baseInfo1(this, 'baseInfo')
      const baseData2 = baseInfo2(this, 'baseInfo')
      let contractCodeList = this.getData()
      return [...baseData1, ...contractCodeList, ...baseData2]
    },
  },
  methods: {
    /**
     * @description:配置合同编码数据展示
     * @param {string} data 获取取值字段
     * @return:
     */
    getData() {
      let contractCodeInfo = []
      let contractCodeList = []
      if (!isEmpty(this.baseInfoForm.contractCodes)) {
        contractCodeList = this.baseInfoForm.contractCodes.split(',')
      }
      if (!contractCodeList || contractCodeList.length <= 1) {
        let flag = contractCodeList && contractCodeList.length
        const [contractCode] = contractCodeList
        contractCodeInfo.push({
          prop: 'contractCode',
          label: '合同编码',
          type: 'render',
          class: flag ? 'font-bold' : 'none-data',
          render: (h, item, parent, { form }) => {
            if (flag) {
              return (
                <el-tooltip
                  effect="dark"
                  content={contractCode}
                  placement="top"
                >
                  <sys-link
                    color="#00a680"
                    underline={false}
                    disabled={true}
                    onClick={() =>
                      this.getContractStyle({
                        type: '',
                        id: '',
                        code: contractCode,
                        style: '',
                      })
                    }
                  >
                    <div class="dst-add-input font-bold">{contractCode}</div>
                  </sys-link>
                </el-tooltip>
              )
            } else {
              return <span> - </span>
            }
          },
        })
      } else {
        let codeList = contractCodeList.map((item, subIndex) => {
          let showIndex = Number(subIndex) + 1
          const name = 'contractCode' + showIndex
          return {
            prop: name,
            label: '合同编码' + showIndex,
            type: 'render',
            class: item ? 'font-bold' : 'none-data',
            render: (h, formItem, parent, { form }) => {
              if (item) {
                return (
                  <el-tooltip effect="dark" content={item} placement="top">
                    <sys-link
                      color="#00a680"
                      underline={false}
                      disabled={true}
                      onClick={() =>
                        this.getContractStyle({
                          type: '',
                          id: '',
                          code: item,
                          style: '',
                        })
                      }
                    >
                      <div class="dst-add-input font-bold">{item}</div>
                    </sys-link>
                  </el-tooltip>
                )
              } else {
                return <span> - </span>
              }
            },
          }
        })
        contractCodeInfo = _.cloneDeep(codeList)
      }
      return contractCodeInfo
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ .el-card__body {
  max-height: inherit !important;
}
</style>
