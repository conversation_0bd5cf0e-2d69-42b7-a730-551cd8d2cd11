<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <DstFiltrate
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      @searchClick="searchClick"
    ></DstFiltrate>
    <div class="dst-table-content">
      <sys-table
        local-key="consignmentRental_entitleServiceMonthlyOrders"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :is-loading="isLock"
        @sizeChange="sizeChange"
        @pageChange="pageChange"
      ></sys-table>
    </div>
    <message-dialog
      :visible.sync="viewFlag"
      :view-type.sync="viewType"
    ></message-dialog>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { getEquityservicemonthpaymentOrderList } from '@/api/consignmentRental/entitleServiceMonthlyOrders'
import { changeVariableName } from '@/utils/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import { addZero } from '@/utils'
export default {
  name: 'OldConsignmentRentalEntitleServiceMonthlyOrders',
  components: {
    MessageDialog: () => import('@/views/common/common/promptDialog'),
  },
  mixins: [mixin, orderMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 权益服务订单总数
      paginationTotal: 0,
      // 弹出提示
      viewFlag: false,
      // 提示类型
      viewType: '',
      // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
      type: 1,
    }
  },
  computed: {
    // 权益服务订单搜索配置项
    setFiltrateData,
    // 权益服务订单列表表头项
    setTableHeadArr,
  },
  methods: {
    /**
     * @description:请求权益服务订单列表数据
     * @param {boolean} 是否需要loading
     * @return:
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      getEquityservicemonthpaymentOrderList(data)
        .then((res) => {
          this.paginationTotal = res.data.totalNum
          this.tableList = res.data.list
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
        parentOrderType: addZero(this.type),
      }
      changeVariableName(params, 'createTime', true, 'Time')
      changeVariableName(params, 'validityBeginTime', true)
      changeVariableName(params, 'validityEndTime', true)
      params.priceStatus = params.priceStatus
        ? Number(params.priceStatus)
        : params.priceStatus
      params.aggregation = params.aggregation ? '1' : '0'
      params.carNoList = this.$toggleSplitJoin(params, 'carNoList') || []
      this.getCustomerIds(params, false)
      this.searchParams = _.cloneDeep(params)
      return params
    },
    /**
     * @description:点击问号弹出提示
     * @param {string} key 当前提示的区分 Agree:履约模块  Validity:有效期模块
     * @return:
     */
    view(key) {
      this.viewType = key
      this.viewFlag = true
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/.label-left {
  margin-right: 10px;
}
</style>
