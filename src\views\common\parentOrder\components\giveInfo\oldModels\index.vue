<template>
  <sys-card title="赠送信息" slot-name="giveInfo" v-loading="isLock">
    <template slot="giveInfo_body">
      <dstForm
        v-if="type == 0 || type == 2"
        ref="ruleForm"
        label-width="140px"
        mode-type="detail"
        v-model="baseInfoForm"
        :data-arr="giveConfig"
      >
        <template slot="releaseRecordId">
          <template
            v-if="
                baseInfoForm.benefitRecordId && !isEmpty(baseInfoForm.benefitRecordId.split(',')) &&
                  baseInfoForm.benefitRecordId.split(',').some(subItem => !!subItem)
              "
          >
            <span v-for="(item, index) in baseInfoForm.benefitRecordId.split(',')" :key="index">
              <template v-if="item">
                <sys-link :underline="false" @click="benefitRecordItem(item)">{{ item }}</sys-link>
                <span v-if="index !== baseInfoForm.benefitRecordId.split(',').length - 1">、</span>
              </template>
            </span>
          </template>
          <span v-else>-</span>
        </template>
      </dstForm>
      <div class="dst-table-content">
        <sys-table
          :table-list="tableList"
          :table-head="giveInfo"
          :total="paginationTotal"
          :page-num="pageNum"
          :page-size="pageSize"
          :is-loading="isLock"
          :is-show-select-btn="false"
          :is-auto-height="false"
          @sizeChange="sizeChange"
          @pageChange="pageChange"
        />
      </div>
    </template>
  </sys-card>
</template>

<script>
/* eslint-disable jsx-quotes */
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { giveInfo } from './functions/table'
import { getParentOrderGiveList } from '@/api/common/rental/parent'
import orderMixins from '@/views/common/common/mixins/orderLink'
import { giveConfig } from './functions/data'
import { isEmpty } from '@/utils'
export default {
  name: 'OldGiveInfo',
  mixins: [mixin, orderMixins],
  props: {
    // 当前父订单code
    parentCode: {
      type: String,
      default: '',
      required: true,
    },
    // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
    type: {
      type: [Number, String],
      default: 0,
      required: true,
    },
    // 赠送信息
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      reuqired: true,
    },
  },
  data() {
    return {
      // loading加载
      isLock: false,
      // 赠送信息列表数据
      tableList: [],
      // 赠送订单总数
      paginationTotal: 0,
    }
  },
  computed: {
    // 赠送列表配置
    giveInfo,
    // 赠送信息表单配置
    giveConfig,
  },
  methods: {
    /**
     * @description:获取赠送列表信息
     * @param {type}
     * @return:
     */
    async getTableData() {
      await this.$nextTick(async () => {
        if (this.parentCode) {
          this.isLock = true
          await getParentOrderGiveList(
            {
              parentOrderCode: this.parentCode,
              pageNum: this.pageNum, //	页码
              pageSize: this.pageSize, //	每页数量
            },
            this.type
          )
            .then((res) => {
              this.tableList = res.data.list || []
              this.paginationTotal = res.data.totalNum
            })
            .finally(() => {
              this.isLock = false
            })
        }
      })
    },
    /**
     * @description:跳转权益卡详情
     * @param {string} item 当前保权益卡数据
     * @return:
     */
    benefitRecordItem(item) {
      if (item) {
        this.$linkTo({
          model: 'cec',
          path: '/goodsCenter/equityCard/recordDetail',
          query: { id: item },
        })
      } else {
        this.$message.warning('缺少权益卡数据信息！')
      }
    },
    /**
     * @description:判断非空
     * @param {type}
     * @return:
     */
    isEmpty,
  },
}
</script>

<style></style>
