import {
  checkSelect,
  isIntegerNumber,
} from '@/portal-common/utils/testedFunc/formValidate'
import { getSimpleDictByType } from '@/api/base'
import batchInput from '@/portal-common/components/BatchSearch'
import DstOrgSelector from '@/portal-common/components/DstOrgSelector/DstOrgSelector'
import { getOrgSplitList } from '@/api/common'
import dictionaries from '@/utils/common.js'
const {
  orderSource,
  isBond,
  carOrderVehicleUse,
  carStatus,
  customerGroupType,
} = dictionaries

/**
 * @description: @description:父订单查询配置项
 */
export function setFiltrateData() {
  return [
    {
      type: batchInput,
      prop: 'parentOrderCodeBatch',
      label: '父订单编码',
      attrs: {
        placeholder: '请输入父订单编码',
        clearable: true,
      },
      width: '350px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'carStatus',
      label: '订单状态',
      optionsApiConf: {
        parmas: { type: carStatus },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderBusinessType',
      label: '客户类型',
      options: [
        { id: 0, name: '企业客户' },
        { id: 1, name: '个人客户' },
      ],
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'customerId',
      label: '客户编码',
      attrs: {
        placeholder: '请输入客户编码',
        clearable: true,
      },
      width: '400px',
      rules: [
        {
          required: false,
          validator: isIntegerNumber({
            required: false,
            customMessage: '客户编码仅支持输入数字类型！',
          }),
          trigger: ['change', 'blur'],
        },
      ],
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerKeys',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        multiple: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      ...this.filterSearchStyle,
      valueType: [],
      on: { change: (val, data) => this.handleChangeCustomer(data, true) },
    },
    {
      type: 'input',
      prop: 'contractCodes',
      label: '合同编码',
      attrs: {
        placeholder: '请输入合同编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: batchInput,
      prop: 'carNoBatch',
      label: '车牌号',
      attrs: {
        placeholder: '请输入车牌号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'vinCode',
      label: '车架号',
      attrs: {
        placeholder: '请输入车架号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'carUse',
      label: '车辆用途',
      optionsApiConf: {
        parmas: { type: carOrderVehicleUse },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'saleName',
      label: '业务归属',
      attrs: {
        placeholder: '请输入业务归属',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'orderChannel',
      label: '订单渠道',
      attrs: {
        placeholder: '请输入订单渠道',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderSource',
      label: '下单渠道',
      optionsApiConf: {
        parmas: { type: orderSource },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: DstOrgSelector,
      label: '服务组织',
      prop: 'regionIds',
      attrs: {
        // params: { startType: 'REGIONAL_LIST', endType: 'STATION_CITY' },
        customRequest: (vm) => {
          // 自定义接口,没有这个属性则用默认接口
          getOrgSplitList({
            startType: 'REGIONAL_LIST',
            endType: 'STATIONS_CITY',
            isAll: 1,
          }).then((res) => {
            // vm为orgSelector实例
            vm.optionsData = res.data || []
          })
        },
      },
      valueType: [],
      width: '310px',
      ...this.filterSearchStyle,
    },
    {
      type: 'cascader',
      prop: 'marketingOrgCodeItems',
      label: '营销组织',
      attrs: {
        placeholder: '请选择营销组织',
        clearable: true,
        // changeOnSelect: true,
        filterable: true,
        showAllLevels: false,
      },
      options: this.marketingOrgCodeItems,
      valueType: [],
      width: '310px',
      props: {
        value: 'code',
        label: 'name',
        children: 'childs',
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerGroupTypeList',
      label: '业务通路',
      optionsApiConf: {
        parmas: { type: customerGroupType },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      attrs: {
        placeholder: '请选择业务通路',
        clearable: true,
        multiple: true,
      },
      valueType: [],
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'isBond',
      label: '保证金',
      optionsApiConf: {
        parmas: { type: isBond },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'expLeaseMonth',
      label: '预计租赁时长（月）',
      labelWidth: '150px',
      width: '300px',
      options: [
        { name: '1-3', id: '1-3' },
        { name: '4-6', id: '4-6' },
        { name: '7-12', id: '7-12' },
        { name: '12+', id: '12-0' },
      ],
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'realLeaseMonth',
      label: '实际租赁时长（月）',
      labelWidth: '150px',
      width: '300px',
      options: [
        { name: '1-3', id: '1-3' },
        { name: '4-6', id: '4-6' },
        { name: '7-12', id: '7-12' },
        { name: '12+', id: '12-0' },
      ],
      ...this.filterSearchStyle,
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        // 要为控件添加属性，写在attrs中，注意这里的控件指的是例如input、select、等等。就是type类型所指的那个控件。仅仅是属性才写在attrs里面哦。
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      labelWidth: '120px',
      valueType: [],
    },
    {
      type: 'DatePicker',
      prop: 'realDeliveryTime',
      label: '实际交车时间',
      width: '400px',
      attrs: {
        // 要为控件添加属性，写在attrs中，注意这里的控件指的是例如input、select、等等。就是type类型所指的那个控件。仅仅是属性才写在attrs里面哦。
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      labelWidth: '120px',
      valueType: [],
    },
    {
      type: 'DatePicker',
      prop: 'realBackCarTime',
      label: '实际还车时间',
      width: '400px',
      attrs: {
        // 要为控件添加属性，写在attrs中，注意这里的控件指的是例如input、select、等等。就是type类型所指的那个控件。仅仅是属性才写在attrs里面哦。
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      labelWidth: '120px',
      valueType: [],
    },
  ]
}

/**
 * @description:合同订单配置项
 * @param {type}
 * @return:
 */
export function dialogDataArr() {
  return [
    {
      prop: 'carNumber',
      label: '提车数量',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
      ],
      rules: [
        {
          required: true,
          validator: checkSelect({ customMessage: '请选择提车数量' }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'cashDeposit',
      label: '保证金',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '收保证金', value: 1 },
        { label: '免保证金', value: 2 },
      ],
      rules: [
        {
          required: true,
          validator: checkSelect({ customMessage: '请选择保证金' }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'performanceCondition',
      label: '履约条件',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '先履约后支付', value: 1 },
        { label: '先履约后结算', value: 2 },
      ],
      rules: [
        {
          required: true,
          validator: checkSelect({ customMessage: '请选择履约条件' }),
          trigger: ['change', 'blur'],
        },
      ],
    },
  ]
}
