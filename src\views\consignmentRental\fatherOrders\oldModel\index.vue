<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <DstFiltrate
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      :form-value="searchValue"
      @searchClick="searchClick"
    ></DstFiltrate>
    <div class="dst-table-content">
      <div
        v-if="btnList.some((item) => !!item.show)"
        class="dst-table-over-btn-box"
      >
        <sys-button :btn-list="btnList" :limit="14"></sys-button>
      </div>
      <sys-table
        local-key="consignmentRental_fatherOrders"
        row-key="orderCode"
        ref="sysTable"
        is-check-box
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :is-loading="isLock"
        @sizeChange="sizeChange"
        @pageChange="pageChange"
        @selection-change="handleSelectionChange"
      />
    </div>
    <hand-car
      v-if="handCarFlag"
      :visible.sync="handCarFlag"
      :list="selectList"
      :customer-info="customerInfo"
      :type="type"
      @refresh="refresh"
    />
    <return-car
      v-if="returnCarFlag"
      :visible.sync="returnCarFlag"
      :list="selectList"
      :type="type"
      :station-list="stationList"
      @refresh="refresh"
    />
    <confirm-collect
      v-if="confirmCollectFlag"
      :visible.sync="confirmCollectFlag"
      :list="selectOrderCodes"
      :type="type"
      :mode="confirmCollectType"
      @refresh="refresh"
    />
    <invalid-dialog
      v-if="invaildFlag"
      :visible.sync="invaildFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="refresh"
    />
    <modify-sales-belong
      v-if="saleBelongFlag"
      :visible.sync="saleBelongFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="refresh"
    />
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { btnList } from '@/views/common/parentOrder/dialog/list/functions/data.js'
import { setTableHeadArr } from './functions/table'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { getParentOrderList } from '@/api/common/rental/parent'
import { changeVariableName, clearObjItem } from '@/utils/business'
import mutltipOptions from '@/views/common/parentOrder/dialog/list/mixins/mutltipOptions.js'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import { isEmpty } from '@/utils'
import marketOrgMixins from '@/views/common/common/mixins/marketOrg.js'
import { equal } from '@/utils/util'
export default {
  name: 'OldConsignmentRentalFatherOrders',
  components: {
    HandCar: () => import('@/views/common/parentOrder/dialog/list/HandCar.vue'),
    ReturnCar: () =>
      import('@/views/common/parentOrder/dialog/list/RefundCar.vue'),
    ConfirmCollect: () =>
      import('@/views/common/parentOrder/dialog/list/ConfirmCollect.vue'),
    InvalidDialog: () =>
      import('@/views/common/parentOrder/dialog/list/InvalidDialog.vue'),
    ModifySalesBelong: () =>
      import('@/views/common/parentOrder/dialog/list/ModifySaleBelong.vue'),
  },
  mixins: [mixin, mutltipOptions, marketOrgMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 父订单总数
      paginationTotal: 0,
      // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
      type: 1,
      // 当前选中的父订单数据
      selectList: [],
    }
  },
  computed: {
    // 父订单搜索配置项
    setFiltrateData,
    // 父订单列表表头项
    setTableHeadArr,
    // 父订单列表按钮配置
    btnList,
  },
  methods: {
    /**
     * @description:请求父订单列表数据
     * @param {type}
     * @return:
     */
    getTableData() {
      if (this.initFlag) {
        this.isLock = true
        const data = this.getParams()
        if (!equal(data, this.searchParams)) {
          this.searchParams = _.cloneDeep(data)
        }
        getParentOrderList(
          {
            ...this.searchParams,
            pageNum: this.pageNum, //	页码
            pageSize: this.pageSize, //	每页数量
          },
          this.type
        )
          .then((res) => {
            this.paginationTotal = res.data.totalNum || 0
            this.tableList = res.data.list || []
            this.initSourceStatus()
          })
          .finally(() => {
            this.isLock = false
          })
      }
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = _.cloneDeep(this.searchData)
      changeVariableName(params, 'createTime', true, 'Time')
      changeVariableName(params, 'realDeliveryTime', true, 'Time')
      changeVariableName(params, 'realBackCarTime', true, 'Time')
      const expLeaseMonth = this.getMouthData(params, 'expLeaseMonth')
      params.expLeaseStartMonth = expLeaseMonth.start
      params.expLeaseEndMonth = expLeaseMonth.end
      const realLeaseMonth = this.getMouthData(params, 'realLeaseMonth')
      params.realLeaseStartMonth = realLeaseMonth.start
      params.realLeaseEndMonth = realLeaseMonth.end
      if (params.regionIds && params.regionIds.length) {
        params.serviceOrgCityCode =
          params.regionIds[params.regionIds.length - 1]
      }
      const { parentOrderCodeBatch, carNoBatch, marketingOrgCodeItems } = params
      parentOrderCodeBatch &&
        (params.parentOrderCodes = parentOrderCodeBatch.split(','))
      carNoBatch && (params.carNoList = carNoBatch.split(','))
      delete params.regionIds
      if (!isEmpty(params.marketingOrgCodeItems)) {
        params.marketingOrgCodeList = [
          marketingOrgCodeItems[marketingOrgCodeItems.length - 1],
        ]
      }
      delete params.marketingOrgCodeItems
      this.getCustomerIds(params, true)
      return clearObjItem(params)
    },
    /**
     * @description:拆分查询项月份数据
     * @param {object} data 当前的数据集合
     * @param {key} string 所对应的字段名称
     * @return:
     */
    getMouthData(data, key) {
      if (data[key]) {
        const arr = data[key].split('-')
        const pararms = {
          start: this.dealData(arr[0]),
          end: this.dealData(arr[1]),
        }
        delete data[key]
        return pararms
      } else {
        delete data[key]
        return {
          start: '',
          end: '',
        }
      }
    },
    /**
     * @description:将数据格式化处理
     * @param {string} data 当前需要格式化的字符串
     * @return:
     */
    dealData(data) {
      return data !== '0' ? (data ? Number(data) : data) : null
    },
    /**
     * @description:APP下单
     * @param {type}
     * @return:
     */
    orderAPP() {
      this.isLock = true
      getParentOrderList()
        .then(async (res) => {
          this.$message.success('下单成功')
          this.getTableData()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:跳转详情页面
     * @param {type}
     * @return:
     */
    goDetail(row) {
      this.$router.push({
        path: '/consignmentRental/fatherOrders/detail',
        query: {
          id: row.id,
          name: 'BaseInfo',
          code: row.orderCode,
          type: this.type,
        },
      })
    },
    /**
     * @description:列表选中触发
     * @param {array} list 当前选中的列表数据
     * @return:
     */
    handleSelectionChange(list) {
      this.selectList = _.cloneDeep(list)
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ .filtrate-cont {
  height: 63px;
  &.open {
    padding-bottom: 10px;
  }
  .el-form-item {
    overflow: visible;
    .el-form-item__error {
      top: 45px;
    }
  }
}
</style>
