import { isEmpty } from '@/utils'
import { isFunction, isString } from '@/utils/types'
import {
  parseExpression,
  checkConditions,
  validateData,
  mergeParentChild,
  processDataNode,
  bfsTraverse,
  processParentField,
} from './functions'
import { FLATTEN_DEFAULT_CONFIG } from './data'

/**
 * 嵌套数据扁平化处理工具
 *
 * 该类用于将具有父子层级关系的嵌套数据结构转换为扁平结构，
 * 同时保留父子项之间的关联关系。支持多种配置选项和数据处理方式。
 *
 * 主要功能：
 * 1. 支持条件过滤：可根据条件选择性地包含/排除节点
 * 2. 支持父子项合并：可配置父项字段如何合并至子项
 * 3. 支持数据验证：可验证数据是否符合规则
 * 4. 支持动态表达式：可根据表达式动态解析配置值
 * 5. 灵活的错误处理：支持多种错误处理策略
 */
class NestedDataFlattener {
  /**
   * 创建扁平化处理器实例
   * @param {Object} options - 配置选项，会与默认配置合并
   */
  constructor(options = {}) {
    // 初始化配置，使用默认配置补充缺失的选项
    this.config = _.defaultsDeep({}, options, FLATTEN_DEFAULT_CONFIG)
    // 表达式缓存，用于提高动态表达式解析性能
    this.exprCache = {}
  }

  /**
   * 核心方法：数据扁平化处理
   *
   * 将嵌套结构的数据转换为扁平结构，同时处理父子项关系
   *
   * @param {Array} data - 嵌套结构的数据数组
   * @returns {Array} 扁平化后的数据数组
   */
  flatten(data) {
    // 检查输入数据有效性
    if (isEmpty(data) || !Array.isArray(data)) return []
    // 初始化结果和错误收集数组
    const result = [],
      errors = []
    try {
      // 使用广度优先遍历处理数据树
      const { errors: traverseErrors } = bfsTraverse({
        data,
        // 节点处理函数：处理当前节点，决定是否继续遍历其子节点
        processNode: (node, context) =>
          this._processNode({ node, context, result, errors }),
        // 获取子节点函数：获取并处理当前节点的子节点
        getChildren: (node, context) =>
          this._getChildren({ node, context, result }),
        // 遍历配置
        options: {
          maxDepth: this.config.maxDepth, // 最大遍历深度
          maxIterations: this.config.maxIterations, // 最大迭代次数
          initialContext: this.config.configContext || {}, // 初始上下文
          onError: (e) =>
            this._log({ level: 'error', msg: '扁平化错误:', data: e }), // 错误处理
        },
      })
      // 合并遍历过程中产生的错误
      !isEmpty(traverseErrors) && errors.push(...traverseErrors)
      // 处理结果并返回
      return this._handleResult({ result, errors })
    } catch (e) {
      // 捕获并处理意外错误
      this._log({ level: 'error', msg: '扁平化错误:', data: e })
      // 安全模式下返回已处理结果，否则返回空数组
      return this.config.safeMode ? result : []
    }
  }

  /**
   * 处理单个节点
   * @param {Object} params - 处理参数
   * @returns {Boolean} 是否继续处理子节点
   * @private
   */
  _processNode({ node, context, result, errors }) {
    const { depth, ctx, parent } = context
    // 条件检查：节点是否满足继续处理的条件
    const isPass = this._checkCondition({
      item: node,
      parent,
      depth,
      ctx,
      type: parent ? 'child' : 'parent',
    })
    if (!isPass) return false // 不满足条件，停止处理
    // 节点处理配置
    const config = {
      preProcess: this.config.preProcess, // 预处理函数
      validate: this.config.strictMode
        ? (item) => this._validate({ item })
        : null, // 验证函数
      skipOnValidationFail: this.config.skipOnConditionFail, // 验证失败是否跳过
      debug: this.config.debug, // 调试模式
    }
    // 处理节点数据，获取处理后的节点、错误和跳过标志
    const {
      node: processed, // 处理后的节点
      errors: nodeErrors, // 处理过程中的错误
      skip, // 是否跳过后续处理
    } = processDataNode(node, config, { depth, parent, ctx })
    // 处理节点验证错误
    !isEmpty(nodeErrors) && errors.push(...nodeErrors)
    if (skip) return false // 需要跳过，停止处理
    // 获取子节点字段名（支持动态）
    const childKey = this._resolveValue({
      val: this.config.childrenKey,
      item: processed,
      parent,
      ctx,
      depth,
    })
    // 获取子节点数组
    const children = _.get(processed, childKey, [])
    // 无子节点情况处理
    if (isEmpty(children)) {
      // 根据配置决定是否保留无子节点的父节点
      if (this.config.keepParentWithoutChildren) result.push(processed)
      return false // 无子节点，停止处理
    }
    return true // 有子节点，继续处理
  }

  /**
   * 获取并处理子节点
   * @param {Object} params - 处理参数
   * @returns {Array} 满足条件的子节点数组
   * @private
   */
  _getChildren({ node, context, result }) {
    const { depth, ctx, parent } = context
    // 获取子节点字段名（支持动态）
    const childKey = this._resolveValue({
      val: this.config.childrenKey,
      item: node,
      parent,
      ctx,
      depth,
    })
    // 获取子节点数组
    const children = _.get(node, childKey, [])
    if (isEmpty(children)) return [] // 无子节点则返回空数组
    // 创建子节点上下文
    const childCtx = { ...ctx, parent: node, depth }
    // 过滤并处理子节点
    return _.filter(children, (child) =>
      this._processChild({
        child,
        parentNode: node,
        depth,
        ctx: childCtx,
        result,
      })
    )
  }

  /**
   * 处理单个子节点
   * @param {Object} params - 处理参数
   * @returns {Boolean} 是否应该递归处理该子节点
   * @private
   */
  _processChild({ child, parentNode, depth, ctx, result }) {
    // 子节点条件检查
    const isChildChecked = this._checkCondition({
      item: child,
      parent: parentNode,
      depth: depth + 1,
      ctx,
      type: 'child',
    })
    if (!isChildChecked) return false // 不满足条件，停止处理
    // 合并父子项属性
    const merged = this._mergeItems({
      child,
      parent: parentNode,
      depth,
    })
    // 添加父ID（如果配置了）
    if (this.config.addParentId && !isEmpty(parentNode)) {
      const pid = _.get(parentNode, this.config.idKey)
      if (!isEmpty(pid)) merged[this.config.parentIdKey] = pid
    }
    // 添加到结果集
    result.push(merged)
    // 返回是否应该继续递归处理
    return this.config.recursive
  }

  /**
   * 合并父子项
   * @param {Object} params - 合并参数
   * @returns {Object} 合并后的对象
   * @private
   */
  _mergeItems({ child, parent, depth }) {
    // 尝试使用自定义合并函数
    if (isFunction(this.config.customMerge)) {
      try {
        // 获取父项属性
        const props = this._getParentProps({ parent })
        // 调用自定义合并函数 - 保持原有参数顺序和格式
        const result = this.config.customMerge(child, parent, props, depth)
        if (result) return result // 返回自定义合并结果
      } catch (e) {
        this._log({ level: 'error', msg: '合并错误:', data: e })
      }
    }
    // 使用默认合并逻辑
    return mergeParentChild(_.clone(child), parent, {
      strategy: this.config.mergeStrategy, // 合并策略
      parentFields: this.config.parentFields, // 父项字段
      parentFieldsMap: this.config.parentFieldsMap, // 父项字段映射
      excludeFields: this.config.excludeParentFields, // 排除字段
      useAllFields: this.config.useAllParentFields, // 使用所有字段
      childrenKey: this.config.childrenKey, // 子项字段名
      transformFn: this.config.transformParentFields, // 字段转换函数
    })
  }

  /**
   * 获取父项属性
   * @param {Object} params - 获取父项属性参数
   * @returns {Object} 提取的属性对象
   * @private
   */
  _getParentProps({ parent }) {
    if (!parent) return {}
    const props = {}
    // 确定需要处理的字段列表
    const fields =
      this.config.useAllParentFields && !isEmpty(this.config.parentFields)
        ? _.keys(_.omit(parent, this.config.excludeParentFields)) // 使用所有字段（排除指定字段）
        : this.config.parentFields // 仅使用指定字段
    // 处理字段的通用函数
    const processFields = (fieldList) => {
      _.forEach(fieldList, (f) => {
        // 获取源字段名（支持字段映射）
        const sourceKey = _.get(this.config.parentFieldsMap, f, f)
        // 判断字段是否应该被处理
        if (_.includes(fields, f) || !_.includes(fieldList, f)) {
          // 使用通用字段处理函数处理单个字段
          const value = processParentField({
            field: f,
            sourceKey,
            parent,
            childrenKey: this.config.childrenKey,
            excludeFields: this.config.excludeParentFields,
            transformFn: this.config.transformParentFields,
            keepOriginalFields: this.config.keepOriginalFields,
            existingValue: props[f],
            onError: (msg, e) => this._log({ level: 'error', msg, data: e }),
          })
          // 添加处理后的值
          if (value !== undefined) props[f] = value
        }
      })
    }
    // 处理主字段列表
    processFields(fields)
    // 处理字段映射中的字段
    processFields(Object.keys(this.config.parentFieldsMap || {}))
    return props
  }

  /**
   * 检查条件
   * @param {Object} params - 检查参数
   * @returns {Boolean} 是否满足条件
   * @private
   */
  _checkCondition({ item, parent, depth, ctx, type }) {
    // 非条件模式下直接返回true
    if (!this.config.conditionalMode) return true
    // 根据类型选择对应的条件函数
    const conditionFn =
      type === 'parent'
        ? this.config.parentCondition
        : this.config.childCondition
    // 使用通用条件检查函数
    return checkConditions({
      item,
      conditionMap: this.config.conditionMap,
      conditionFn,
      context: { parent, depth, ...ctx },
    })
  }

  /**
   * 验证数据
   * @param {Object} params - 验证参数
   * @returns {Object} 验证结果 {valid: Boolean, errors: Array}
   * @private
   */
  _validate({ item }) {
    return validateData({
      item,
      requiredFields: this.config.requiredFields, // 必填字段
      typeMap: this.config.typeMap, // 类型映射
      validationFn: this.config.validationFn, // 自定义验证函数
    })
  }

  /**
   * 解析配置值
   * @param {Object} params - 解析参数
   * @returns {*} 解析后的值
   * @private
   */
  _resolveValue({ val, item, parent, ctx, depth }) {
    // 非动态配置模式直接返回原值
    if (!this.config.dynamicConfig) return val
    // 字符串类型可能包含表达式
    if (isString(val)) {
      const expr = val
      const context = { item, parent, context: ctx, depth }
      const options = {
        prefix: this.config.expressionPrefix, // 表达式前缀
        suffix: this.config.expressionSuffix, // 表达式后缀
        functions: this.config.expressionFunctions, // 可用函数
        cache: this.exprCache, // 表达式缓存
      }
      // 解析表达式
      const result = parseExpression(expr, context, options)
      // 如果结果是函数，则执行函数
      return isFunction(result) ? result(item, parent, ctx, depth) : val
    }
    return val
  }

  /**
   * 处理结果和错误
   * @param {Object} params - 处理参数
   * @returns {Array} 最终处理结果
   * @private
   */
  _handleResult({ result, errors }) {
    // 尝试使用后处理函数
    if (isFunction(this.config.postProcess)) {
      try {
        const processed = this.config.postProcess(result, errors)
        if (processed !== undefined) return processed // 恢复原有逻辑判断
      } catch (e) {
        this._log({ level: 'error', msg: '后处理错误:', data: e })
      }
    }
    // 严格模式下处理错误
    const isStrictError =
      this.config.strictMode && !isEmpty(errors) && this.config.throwOnError
    if (isStrictError) throw new Error(`数据错误: ${errors.join('; ')}`)
    return result
  }

  /**
   * 日志记录
   * @param {Object} params - 日志参数
   * @private
   */
  _log({ level, msg, data }) {
    // 仅调试模式下输出日志
    if (this.config.debug) console[level](msg, data)
    // 错误级别且配置为抛出错误时抛出异常
    if (level === 'error' && this.config.throwOnError) {
      throw data instanceof Error ? data : new Error(msg)
    }
  }

  /**
   * 静态方法：一步完成扁平化
   * @param {Array} data - 嵌套结构数据
   * @param {Object} options - 配置选项
   * @returns {Array} 扁平化后的数据
   * @static
   */
  static flattenNestedData(data = [], options = {}) {
    return new NestedDataFlattener(options).flatten(data)
  }
}

/**
 * 导出通用嵌套数据扁平化函数
 * 便于直接使用的静态函数
 */
export const flattenNestedData = NestedDataFlattener.flattenNestedData
