# 订单类型 orderCategory 统一配置方案

## 问题描述

在所有业务模块（consignmentRental、carRental、happlyRental、equityServices、escrowCollect）中，每个订单类型的 index.vue 和 detail.vue 文件都需要使用相同的 `orderCategory` 值，但之前这些值是硬编码在各个文件中的，导致：

1. 代码重复，维护困难
2. 如果需要修改某个订单类型的 orderCategory，需要同时修改 index.vue 和 detail.vue 两个文件
3. 容易出现不一致的情况
4. 跨业务模块的相同订单类型可能使用不同的 orderCategory 值

## 解决方案

### 1. 创建统一配置

在 `src/utils/system/constant.js` 中添加了 `consignmentRentalOrderCategories` 配置对象和 `getConsignmentRentalOrderCategory` 工具函数：

```javascript
// 先租后买订单类型对应的 orderCategory 配置
export const consignmentRentalOrderCategories = {
  // 先租后买订单
  consignmentRentalOrders: 1,
  // 里程保证金订单
  mileageMarginOrders: 12,
  // 续保保证金订单
  renewalMarginOrders: 14,
  // 还款保证金订单
  repayMarginOrders: 15,
  // 退车定损订单
  returnCarOrders: 4,
  // 解约违约金订单
  breachPenaltyOrders: 13,
  // 权益卡订单
  equityServiceOrders: 6,
  // 违章代办订单
  violationOrders: 22,
  // 增值服务订单
  valueAddedServiceOrders: 20,
  // 过户订单
  transferOwnershipOrders: 24,
}

/**
 * 获取先租后买订单类型对应的 orderCategory
 * @param {string} orderType - 订单类型键名
 * @returns {number} orderCategory 值
 */
export const getConsignmentRentalOrderCategory = (orderType) => {
  return consignmentRentalOrderCategories[orderType] || 1
}
```

### 2. 使用方式

#### 方式一：直接使用配置对象（推荐用于已知的固定类型）

```javascript
import { consignmentRentalOrderCategories } from '@/utils/system/constant'

// 在 getParams 方法中使用
getParams() {
  // ...
  return {
    // ...
    ...getParams({
      data: params,
      queryItems: [1, 2, 3, 5],
      orderCategory: consignmentRentalOrderCategories.mileageMarginOrders,
      orderBusinessType: 'rent_for_sale',
    })
  }
}
```

#### 方式二：使用工具函数（推荐用于动态获取）

```javascript
import { getConsignmentRentalOrderCategory } from '@/utils/system/constant'

// 在 getParams 方法中使用
getParams() {
  // ...
  return {
    // ...
    ...getParams({
      data: params,
      queryItems: [1, 2, 3, 5],
      orderCategory: getConsignmentRentalOrderCategory('mileageMarginOrders'),
      orderBusinessType: 'rent_for_sale',
    })
  }
}
```

### 3. 已完成的文件修改

以下文件已经完成了配置化改造：

#### 里程保证金订单 (mileageMarginOrders)
- ✅ `src/views/consignmentRental/mileageMarginOrders/index.vue`
- ✅ `src/views/consignmentRental/mileageMarginOrders/detail.vue`

#### 续保保证金订单 (renewalMarginOrders)
- ✅ `src/views/consignmentRental/renewalMarginOrders/index.vue`
- ✅ `src/views/consignmentRental/renewalMarginOrders/detail.vue`

#### 还款保证金订单 (repayMarginOrders)
- ✅ `src/views/consignmentRental/repayMarginOrders/index.vue`
- ✅ `src/views/consignmentRental/repayMarginOrders/detail.vue`

#### 先租后买订单 (consignmentRentalOrders)
- ✅ `src/views/consignmentRental/consignmentRentalOrders/index.vue`
- ✅ `src/views/consignmentRental/consignmentRentalOrders/detail.vue`

#### 权益服务订单（次付）(entitleServiceDisposableOrders)
- ✅ `src/views/consignmentRental/entitleServiceDisposableOrders/index.vue`
- ✅ `src/views/consignmentRental/entitleServiceDisposableOrders/detail.vue`

#### 权益服务订单（月付）(entitleServiceMonthlyOrders)
- ✅ `src/views/consignmentRental/entitleServiceMonthlyOrders/index.vue`
- ✅ `src/views/consignmentRental/entitleServiceMonthlyOrders/detail.vue`

#### 预付款订单 (vehicleDepositOrders)
- ✅ `src/views/consignmentRental/vehicleDepositOrders/index.vue`
- ✅ `src/views/consignmentRental/vehicleDepositOrders/detail.vue`

#### 退车定损订单 (returnCarOrders)
- ✅ `src/views/consignmentRental/returnCarOrders/index.vue`

#### 增值服务订单 (valueAddedServiceOrders)
- ✅ `src/views/consignmentRental/valueAddedServiceOrders/index.vue`

#### 解约违约金订单 (breachPenaltyOrders)
- ✅ `src/views/consignmentRental/breachPenaltyOrders/index.vue`

#### 权益卡订单 (equityServiceOrders)
- ✅ `src/views/consignmentRental/equityServiceOrders/index.vue`

### 4. 待完成的文件

其他订单类型可以按照相同的模式进行改造：

- `returnCarOrders` (orderCategory: 4)
- `breachPenaltyOrders` (orderCategory: 13)
- `equityServiceOrders` (orderCategory: 6)
- `violationOrders` (orderCategory: 22)
- `valueAddedServiceOrders` (orderCategory: 20)
- `transferOwnershipOrders` (orderCategory: 24)

### 5. 优势

1. **统一管理**：所有 orderCategory 配置集中在一个文件中
2. **避免重复**：index.vue 和 detail.vue 使用相同的配置源
3. **易于维护**：修改 orderCategory 只需要在配置文件中修改一处
4. **类型安全**：通过工具函数可以提供更好的错误处理
5. **向后兼容**：不影响现有功能，只是改变了数据来源

### 6. 注意事项

1. 确保配置文件中的键名与实际的订单类型目录名保持一致
2. 如果添加新的订单类型，需要在配置对象中添加对应的 orderCategory
3. 建议使用工具函数 `getConsignmentRentalOrderCategory` 以获得更好的错误处理
