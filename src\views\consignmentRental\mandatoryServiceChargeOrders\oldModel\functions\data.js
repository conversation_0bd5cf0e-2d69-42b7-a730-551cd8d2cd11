import { getSimpleDictByType } from '@/api/base'
import BatchInput from '@/portal-common/components/BatchSearch'
import dictionaries from '@/utils/common.js'
const { orderStatus } = dictionaries

/**
 * @description:强制收车服务费订单查询配置项
 */
export function setFiltrateData() {
  return [
    {
      type: 'input',
      prop: 'orderCode',
      label: '子订单编码',
      attrs: {
        placeholder: '请输入子订单编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'parentOrderCode',
      label: '父订单编码',
      attrs: {
        placeholder: '请输入父订单编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerKey',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      on: { change: (val, data) => this.handleChangeCustomer(data, false) },
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'contractCode',
      label: '合同编码',
      attrs: {
        placeholder: '请输入合同编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: BatchInput,
      prop: 'carNoList',
      label: '车牌号',
      attrs: {
        placeholder: '请输入车牌号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'vinCode',
      label: '车架号',
      attrs: {
        placeholder: '请输入车架号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderStatus',
      label: '订单状态',
      optionsApiConf: {
        parmas: { type: orderStatus },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px'
    },
  ]
}
