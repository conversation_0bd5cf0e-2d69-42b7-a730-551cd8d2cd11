<template>
  <div>
    <!-- 基础信息 -->
    <base-info-detail ref="baseInfo" v-bind="$attrs" v-loading="isLock"></base-info-detail>
    <!-- 客户信息 -->
    <customer-info
      ref="customerInfo"
      :type="type"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="isLock"
    ></customer-info>
    <!-- 管理备注 -->
    <management-note :type="type" v-bind="$attrs" v-on="$listeners" v-loading="isLock"></management-note>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import BaseInfoDetail from '../components/BaseInfo.vue'
import orderMixins from '@/views/common/common/mixins/orderLink'
import CustomerInfo from '@/views/common/parentOrder/components/customerInfo'
import ManagementNote from '@/views/common/parentOrder/components/managementNote'
export default {
  name: 'BaseInfo',
  components: {
    BaseInfoDetail,
    CustomerInfo,
    ManagementNote,
  },
  mixins: [orderMixins],
  props: {
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type: {
      type: [Number, String],
      default: '2',
    },
  },
  data() {
    return {
      // loading加载
      isLock: false,
    }
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
