/**
 * @description:车辆保证金订单列表数据
 */
export function setTableHeadArr() {
  return [
    { label: '子订单编码', prop: 'orderCode', minWidth: 220 },
    {
      label: '父订单编码',
      prop: 'parentOrderCode',
      minWidth: 220,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.parentOrderCode,
          data: row,
          orderType: 0,
        })
      },
    },
    { label: '客户编码', prop: 'customerId', minWidth: 180 },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '合同编码', prop: 'contractCode', minWidth: 300 },
    {
      label: '是否有效订单',
      prop: 'validOrder',
      minWidth: 120,
      type: 'translate',
      // 子订单状态0:无效1:有效
      options: { 0: '否', 1: '是' },
    },
    { label: '合同乙方', prop: 'contractPartyName', minWidth: 220 },
    {
      label: '应付金额',
      prop: 'payFee',
      minWidth: 130,
      type: 'money',
    },
    { label: '车牌号', prop: 'carNo', minWidth: 120 },
    { label: '车架号', prop: 'vinCode', minWidth: 220 },
    { label: '订单状态', prop: 'orderStatusName', minWidth: 150 },
    { label: '创建时间', prop: 'createTime', minWidth: 180 },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '详情',
          click: ({ row }) =>
            this.goDetail({
              value: row.orderCode,
              data: row,
              orderType: row.orderType,
            }),
        },
      ],
    },
  ]
}
