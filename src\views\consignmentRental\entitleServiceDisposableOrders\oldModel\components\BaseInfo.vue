<template>
  <sys-card title="基础信息" slot-name="baseInfoForm">
    <template slot="baseInfoForm_body">
      <dstForm
        ref="ruleForm"
        label-width="140px"
        mode-type="detail"
        v-model="baseInfoForm"
        :data-arr="baseInfo"
      ></dstForm>
    </template>
  </sys-card>
</template>

<script>
/* eslint-disable jsx-quotes */
import orderMixins from '@/views/common/common/mixins/orderLink'
import contractMixins from '@/views/common/common/mixins/contractMixins'
export default {
  name: 'BaseInfo',
  mixins: [orderMixins, contractMixins],
  props: {
    // 基本信息数据
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      required: true,
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type: {
      type: [String, Number],
      default: 1,
      required: true,
    },
  },
  computed: {
    // 基础信息详情配置
    baseInfo() {
      return [
        {
          prop: 'orderCode',
          label: '订单编号',
          type: 'text',
          class: 'font-bold',
        },
        {
          prop: 'createTime',
          label: '创建时间',
          type: 'text',
        },
        {
          prop: 'cancelTime',
          label: '取消时间',
          type: 'text',
        },
        {
          prop: 'orderStatusName',
          label: '订单状态',
          type: 'text',
          class: 'font-bold',
        },
        {
          prop: 'orderTypeName',
          label: '订单类型',
          type: 'text',
          class: 'font-bold',
        },
        {
          prop: 'parentOrderCode',
          label: '关联父订单',
          type: 'render',
          class: this.baseInfoForm.parentOrderCode ? 'font-bold' : 'none-data',
          render: (h, item, parent, { form }) => {
            const parentOrderCode = this.baseInfoForm.parentOrderCode
            return parentOrderCode ? (
              <el-tooltip
                effect="dark"
                content={parentOrderCode}
                placement="top"
              >
                <sys-link
                  underline={false}
                  onClick={() =>
                    this.goDetail({
                      value: this.baseInfoForm.parentOrderCode,
                      data: this.baseInfoForm,
                      orderType: 0,
                    })
                  }
                >
                  <div class="dst-add-input font-bold">{parentOrderCode}</div>
                </sys-link>
              </el-tooltip>
            ) : (
              <span> - </span>
            )
          },
        },
        {
          prop: 'contractCode',
          label: '合同编码',
          type: 'render',
          class: this.baseInfoForm.contractCode ? 'font-bold' : 'none-data',
          render: (h, item, parent, { form }) => {
            const { contractCode, contractType, contractId, contractStyle } =
              this.baseInfoForm
            return contractCode ? (
              <el-tooltip effect="dark" content={contractCode} placement="top">
                <sys-link
                  underline={false}
                  disabled={contractType == 9}
                  color={contractType == 9 ? '#00a680' : ''}
                  onClick={() =>
                    this.getContractStyle({
                      type: contractType,
                      id: contractId,
                      code: contractCode,
                      style: contractStyle,
                    })
                  }
                >
                  <div class="dst-add-input font-bold">{contractCode}</div>
                </sys-link>
              </el-tooltip>
            ) : (
              <span> - </span>
            )
          },
        },
        {
          prop: 'invoiceName',
          label: '开票主体',
          type: 'text',
        },
        {
          prop: 'operateName',
          label: '运营主体',
          type: 'text',
        },
        {
          prop: 'carNo',
          label: '车牌号',
          type: 'text',
          class: 'font-bold',
        },
        {
          prop: 'vinCode',
          label: '车架号',
          type: 'text',
          class: 'font-bold',
        },
        {
          prop: 'payModeName',
          label: '首次支付范围',
          type: 'text',
        },
        {
          prop: 'cancelReason',
          label: '取消原因',
          type: 'text',
        },
      ]
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
