/* eslint-disable jsx-quotes */
import ShowTime from '@/views/common/rental/common/showTime'

/**
 * @description:基础信息
 * @param {type}
 * @return:
 */
export function baseInfo(v) {
  return [
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'text',
    },
    {
      prop: 'createTime',
      label: '创建时间',
      type: 'text',
    },
    {
      prop: 'cancelTime',
      label: '取消时间',
      type: 'text',
    },
    {
      prop: 'orderStatusName',
      label: '订单状态',
      type: 'text',
    },
    {
      prop: 'orderTypeName',
      label: '订单类型',
      type: 'text',
    },
    {
      prop: 'parentOrderId',
      label: '关联父订单',
      type: 'text',
      class: v.baseInfoForm.parentOrderId ? 'dst-green-underline' : 'none-data',
      isTooltip: true,
      click: () => {
        v.goDetail({
          value: v.baseInfoForm.parentOrderId,
          data: v.baseInfoForm,
          orderType: 0,
        })
      },
    },
    {
      prop: 'contractCode',
      label: '合同编码',
      type: 'text',
      // class: v.baseInfoForm.contractCode ? 'dst-green-underline' : 'none-data',
      // isTooltip: true,
      // click: () => {
      //   v.goDetail({
      //     value: v.baseInfoForm.contractCode,
      //     data: v.baseInfoForm,
      //     orderType: 'contract'
      //   })
      // }
    },
    {
      prop: 'invoiceName',
      label: '开票主体',
      type: 'text',
    },
    {
      prop: 'operateName',
      label: '运营主体',
      type: 'text',
    },
    {
      prop: 'carNo',
      label: '车牌号',
      type: 'text',
    },
    {
      prop: 'vinCode',
      label: '车架号',
      type: 'text',
    },
    {
      prop: 'validityTime',
      label: '订单有效期',
      type: 'renderMemberItem',
      render: (h, ctx) => {
        return (
          <ShowTime
            text="订单有效期"
            component-key="Validity"
            config={{
              data: [
                v.baseInfoForm.validityBeginTime,
                v.baseInfoForm.validityEndTime,
              ],
              showEmpty: true,
              spliter: '~',
              type: 'time',
              strict: false,
            }}
          ></ShowTime>
        )
      },
    },
    // {
    //   prop: 'fulfillmentTime',
    //   label: '订单履约时间',
    //   type: 'renderMemberItem',
    //   render: (h, ctx) => {
    //     return (
    //       <ShowTime
    //         text='订单履约时间'
    //         component-key='Agree'
    //         config={{
    //           data: [
    //             v.baseInfoForm.fulfillmentBeginTime,
    //             v.baseInfoForm.fulfillmentEndTime,
    //           ],
    //           showEmpty: true,
    //           spliter: '~',
    //           type: 'time',
    //           strict: false,
    //         }}
    //       ></ShowTime>
    //     )
    //   },
    // },
    {
      prop: 'payModeName',
      label: '首次支付范围',
      type: 'text',
    },
    {
      prop: 'isReletName',
      label: '是否续租',
      type: 'text',
    },
    {
      prop: 'cancelReason',
      label: '取消原因',
      type: 'text',
    },
  ]
}
