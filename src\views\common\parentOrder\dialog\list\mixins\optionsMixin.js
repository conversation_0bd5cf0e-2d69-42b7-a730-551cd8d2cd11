import {
  checkPickCustomer,
  verificationCode,
  isTradeGrayJudge,
} from '@/api/common/rental/parent.js'
import { isEmpty, getChain, delEmptyChild } from '@/utils'
import { getServiceOrgStationsByEnable } from '@/api/common'
import { isFunction } from '@/utils/types'

export default {
  data() {
    return {
      // 是否打开交车预约弹框
      handCarFlag: false,
      // 是否打开退车预约弹框
      returnCarFlag: false,
      // 是否打开修改销售归属弹框
      saleBelongFlag: false,
      // 获取选中的提车客户信息
      customerInfo: {},
      // 是否打开确认收款弹框
      confirmCollectFlag: false,
      // 是否打开作废弹框
      invaildFlag: false,
      // 区分交车收款与过户收款
      confirmCollectType: 'handle',
      // 是否打开修改提车场站弹框
      modifyStationFlag: false,
      // 来源数据存储
      sourceInfo: {},
      // 场站数据列表
      stationList: [],
    }
  },
  computed: {
    // 过滤父订单选中数据
    selectOrderCodes() {
      return this.selectList.map((item) => item.orderCode)
    },
  },
  created() {
    this.initSource(() => this.getTableData(this.initSourceStatus))
  },
  activated() {
    this.initSource(() => this.getTableData(this.initSourceStatus))
  },
  methods: {
    /**
     * @description:交车预约
     */
    handCarAppoint() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请先选择需要操作的订单！')
      }
      if (!this.repeat(this.selectList, 'customerId')) {
        return this.$message.warning('非同一个提车客户，不能下发交车工单！')
      }
      if (!this.repeat(this.selectList, 'carUse')) {
        return this.$message.warning('非同一个车辆用途，不能下发交车工单！')
      }
      // ORDER_CAR_STATUS_1(1, '待支付'),
      // ORDER_CAR_STATUS_2(2, '待交车'),
      // ORDER_CAR_STATUS_3(3, '交车中'),
      // ORDER_CAR_STATUS_4(4, '已交车'),
      // ORDER_CAR_STATUS_5(5, '还车中'),
      // ORDER_CAR_STATUS_6(6, '已还车'),
      // ORDER_CAR_STATUS_7(7, '已取消'),
      // ORDER_CAR_STATUS_8(8, '已退款'),
      // ORDER_CAR_STATUS_9(9, '待替换'),
      // ORDER_CAR_STATUS_10(10, '替换中'),
      // ORDER_CAR_STATUS_11(11, '待录合同')
      if (this.selectList.some((item) => item.orderSource != 2)) {
        return this.$message.warning('非铁牛下单渠道不能下发预约单！')
      }
      if (this.selectList.some((item) => item.carStatus != 2)) {
        return this.$message.warning(
          '下发预约交车失败，全部选中的父订单状态必须为待交车！'
        )
      }
      this.isLock = true
      checkPickCustomer(this.selectOrderCodes, this.type)
        .then((res) => {
          this.customerInfo = res.data || {}
          this.handCarFlag = true
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:获取选中的数据并去除重复项
     * @param {array} list 选中的数据
     * @param {string} prop 数据属性
     * @return {boolean} 是否存在重复项目
     */
    repeat(list, prop) {
      const arr = list.map((item) => item[prop])
      return [...new Set(arr)].length === 1
    },
    /**
     * @description:退车预约
     */
    returnCarAppoint() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请先选择需要操作的订单！')
      }
      if (!this.repeat(this.selectList, 'customerId')) {
        return this.$message.warning('非同一个提车客户，不能下发退车工单！')
      }
      if (!this.repeat(this.selectList, 'cityCode')) {
        return this.$message.warning('非同一个城市，不能下发退车工单！')
      }
      if (this.selectList.some((item) => item.carStatus != 4)) {
        return this.$message.warning(
          '下发预约还车失败，全部选中的父订单状态必须为已交车！'
        )
      }
      this.initStation()
    },
    /**
     * @description:修改销售归属
     */
    modifySalesBelong() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请先选择需要操作的订单！')
      }
      this.saleBelongFlag = true
    },
    /**
     * @description:续租
     */
    relet() {
      this.$router.push({
        name: 'carRentalFatherOrderRelet',
        query: { type: this.type },
      })
    },
    /**
     * @description:交车确认收款
     */
    handleConfirmCollect() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请先选择需要操作的订单！')
      }
      this.isLock = true
      this.confirmCollectType = 'handle'
      if (this.type == 0) {
        isTradeGrayJudge({
          grayCode: 'B2B20230925001',
          customerId: this.selectList[0].customerId,
        })
          .then((res) => {
            if (res.data) this.confirmCollectType = 'trade'
          })
          .finally(() => {
            this.confirmCollect()
          })
      } else {
        this.confirmCollect()
      }
    },
    /**
     * @description:作废
     */
    invaild() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请先选择需要操作的订单！')
      }
      if (this.selectList.some((item) => item.carStatus == 7)) {
        return this.$message.warning('已取消的订单不能作废！')
      }
      if (
        this.selectList.some(
          // 0 企业 1个人
          (item) =>
            item.orderSource == 2 &&
            (item.carStatus == 1 || item.carStatus == 2)
        )
      ) {
        this.invaildFlag = true
      } else {
        this.$message.warning('仅铁牛下单渠道待支付、待交车订单可作废！')
      }
    },
    /**
     * @description:发送提车码
     * @param {type}
     * @return:
     */
    sendCode() {
      if (this.selectList.length === 0) {
        return this.$message.warning('请先选择需要操作的订单！')
      }
      if (
        this.selectList.some((item) => {
          return !['3', '10'].includes(item.carStatus.toString())
        })
      ) {
        // ORDER_CAR_STATUS_1(1, '待支付'),
        // ORDER_CAR_STATUS_2(2, '待交车'),
        // ORDER_CAR_STATUS_3(3, '交车中'),
        // ORDER_CAR_STATUS_4(4, '已交车'),
        // ORDER_CAR_STATUS_5(5, '还车中'),
        // ORDER_CAR_STATUS_6(6, '已还车'),
        // ORDER_CAR_STATUS_7(7, '已取消'),
        // ORDER_CAR_STATUS_8(8, '已退款'),
        // ORDER_CAR_STATUS_9(9, '待替换'),
        // ORDER_CAR_STATUS_10(10, '替换中'),
        // ORDER_CAR_STATUS_11(11, '待录合同'),
        // ORDER_CAR_STATUS_14(14, '待转租'),
        // ORDER_CAR_STATUS_15(15, '已转租')
        return this.$message.warning(
          '发送提车码失败，全部选中的父订单状态必须为交车中或替换中！'
        )
      }
      this.isLock = true
      verificationCode(
        {
          parentOrderCodeList: this.selectOrderCodes,
        },
        this.type
      )
        .then((res) => {
          if (res.data) {
            // this.$message.success('已发送提车码！')
            const h = this.$createElement
            this.$msgbox({
              title: '提示',
              message: h('div', null, [
                h('p', null, '提车码已做补发处理 '),
                h('p', null, '短信发送记录可在消息中心查看'),
                h('p', null, '短信发送记录预计3分钟内更新，请稍后、刷新'),
              ]),
              showCancelButton: true,
              confirmButtonText: '查看短信记录',
              cancelButtonText: '完成',
              beforeClose: (action, instance, done) => {
                this.resetRefresh()
                done()
              },
            }).then(() => {
              this.$linkTo({
                model: 'information',
                path: '/shortMessageService/sendTask/detail',
                query: {
                  id: res.data,
                },
              })
            })
          } else {
            this.$alert(res.message || '提车码发送失败', '提示', {
              confirmButtonText: '确定',
            })
          }
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:过户确认收款
     * @param {type}
     * @return:
     */
    transterConfirmCollect() {
      if (!this.selectList || this.selectList.length !== 1) {
        return this.$message.warning('请先选择1条要操作的订单！')
      }
      this.confirmCollectType = 'transfer'
      this.confirmCollect()
    },
    /**
     * @description:确认收款
     */
    confirmCollect() {
      this.isLock = false
      if (['handle', 'trade'].includes(this.confirmCollectType)) {
        if (
          this.selectList.some(
            // 0 企业 1个人
            (item) => item.orderSource != 2 || item.carStatus != 1
          )
        ) {
          return this.$message.warning(
            '铁牛平台待支付类型订单才可发起确认收款！'
          )
        }
      } else {
        if (this.selectList.some((item) => item.carStatus != 4)) {
          return this.$message.warning('全部选中的父订单状态必须为已交车！')
        }
      }
      this.confirmCollectFlag = true
    },
    /**
     * @description:修改提车场站
     */
    changeStation() {
      if (!this.selectList.length) {
        return this.$message.warning('请选择需要修改场站的订单数据！')
      }
      if (this.selectList.length > 1) {
        return this.$message.warning('只能选择1条修改场站的订单数据！')
      }
      const { shopId, carStatus } = this.selectList[0]
      if (carStatus != 1 || !shopId) {
        return this.$message.warning(
          'BBC租赁订单、父订单处于待支付状态才能进行修改提车场站信息！'
        )
      }
      this.modifyStationFlag = true
    },
    /**
     * @description:初始化来源数据
     * @param {function} fn 初始化回调
     */
    initSource(fn) {
      const sourceInfo = this.$route.query || {}
      let target = _.cloneDeep(this.sourceInfo)
      if (!_.isEqual(sourceInfo, target)) {
        target = this.$route.query || {}
      }
      const { parentOrderCode, source } = target || {}
      if (parentOrderCode && source) {
        this.sourceInfo = { carStatus: '4', ...target }
      }
      isFunction(fn) && fn()
    },
    /**
     * @description:初始化请求数据后的状态校验
     */
    initSourceStatus() {
      this.isStart = false
      const { parentOrderCode, source } = this.sourceInfo || {}
      if (parentOrderCode && source) {
        if (isEmpty(this.tableList)) {
          return this.$message.warning(
            '当前订单数据不存在或订单状态不为已交车！'
          )
        }
        if (
          this.btnList.some(
            (item) => item.slot === 'returnCarAppoint' && item.show
          )
        ) {
          this.toggleSelection(this.tableList)
          this.$nextTick(this.returnCarAppoint)
        } else {
          this.$message.warning('当前订单类型不支持退车预约功能！')
        }
      }
    },
    /**
     * @description:初始化场站数据
     * @param {type}
     * @return:
     */
    initStation() {
      this.isLock = true
      getServiceOrgStationsByEnable()
        .then((res) => {
          this.stationList = delEmptyChild(res.data || [])
          this.selectList = this.selectList.map((item) => {
            const { value, label, items } = getChain({
              value: this.sourceInfo.stationId || item.stationId,
              data: this.stationList,
              props: { id: 'orgId', children: 'childs', name: 'name' },
              callback: (data) => data.name === item.stationName,
            })
            return {
              ...item,
              stationIds: (items || []).map((subItem) => subItem.orgCode),
              enforce: this.sourceInfo.enforce,
              stationId: value,
              stationName: label,
            }
          })
        })
        .finally(() => {
          this.returnCarFlag = true
          this.isLock = false
        })
    },
  },
}
