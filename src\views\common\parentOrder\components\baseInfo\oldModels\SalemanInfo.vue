<template>
  <div class="recommend-info dst-table-content">
    <sys-table
      :table-list="showList"
      :table-head="salemanConfig"
      :total="paginationTotal"
      :page-num="pageNum"
      :page-size="pageSize"
      :is-loading="isLock"
      :is-show-select-btn="false"
      :is-auto-height="false"
      :index-method="indexMethod"
      @sizeChange="sizeChange"
      @pageChange="pageChange"
    />
  </div>
</template>

<script>
import { salemanConfig } from './functions/data'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import cosutomerMixins from '@/views/common/common/mixins/cosutomerMixins.js'
export default {
  name: 'SalemanInfo',
  mixins: [mixin, cosutomerMixins],
  props: {
    // 业务归属信息数据
    list: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      // 业务归属信息展示
      tableList: [],
      // 数据总数
      paginationTotal: 0,
      // 展示列表数据
      showList: [],
    }
  },
  computed: {
    // 业务归属列表配置
    salemanConfig,
  },
  watch: {
    data: {
      handler() {
        this.initData()
      },
      deep: true,
    },
  },
  created() {
    this.initData()
  },
  methods: {
    /**
     * @description:索引变化
     * @param {number} index 当前索引项
     * @return:
     */
    indexMethod(index) {
      return (this.pageNum - 1) * this.pageSize + index + 1
    },
    /**
     * @description:格式化数据
     * @param {type}
     * @return:
     */
    initData() {
      if (this.list && this.list.length) {
        this.tableList = _.cloneDeep(this.list).map((item) => ({
          ...item,
          typeName: !this.$isEmpty(item.type)
            ? item.type == 1
              ? '客户成功'
              : '销售'
            : '',
        }))
        this.paginationTotal = this.list.length
        this.getTableData()
      }
    },
    /**
     * @description:显示当前列表数据项
     * @param {type}
     * @return:
     */
    getTableData() {
      this.showList = this.tableList.slice(
        (this.pageNum - 1) * this.pageSize,
        this.pageNum * this.pageSize
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.recommend-info {
  width: 100%;
}
</style>
