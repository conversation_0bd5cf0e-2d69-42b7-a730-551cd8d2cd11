<template>
  <div class="recommend-info dst-table-content">
    <sys-table
      is-load-more
      :table-list="tableList"
      :table-head="recommendConfig"
      :total="paginationTotal"
      :page-num="pageNum"
      :page-size="pageSize"
      :is-loading="isLock"
      :is-show-select-btn="false"
      :is-auto-height="false"
      :is-show-pagination="false"
    />
  </div>
</template>

<script>
import { recommendConfig } from './functions/data'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import cosutomerMixins from '@/views/common/common/mixins/cosutomerMixins.js'
export default {
  name: 'RecommendInfo',
  mixins: [mixin, cosutomerMixins],
  props: {
    // 推荐信息数据
    data: {
      type: Object,
      default: () => ({}),
      required: true
    }
  },
  data() {
    return {
      // 推荐信息展示
      tableList: [],
      // 数据总数
      paginationTotal: 0
    }
  },
  computed: {
    // 推荐列表配置
    recommendConfig
  },
  watch: {
    data: {
      handler() {
        this.initData()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /**
     * @description:格式化数据
     * @param {type}
     * @return:
     */
    initData() {
      if (this.data && Object.keys(this.data).length) {
        const { saleName, userId, inviteCode, phone } = this.data
        this.tableList = [{ saleName, userId, inviteCode, phone }]
        this.paginationTotal = this.tableList.length
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.recommend-info {
  width: 100%;
}
</style>
