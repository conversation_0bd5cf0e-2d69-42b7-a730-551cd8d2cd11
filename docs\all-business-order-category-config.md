# 全业务模块订单类型 orderCategory 统一配置方案

## 🎯 解决方案概述

为了解决各业务模块中 orderCategory 硬编码导致的维护困难问题，我们在 `src/utils/system/constant.js` 中创建了统一的配置管理方案。

## 📋 配置内容

### 1. 先租后买 (consignmentRental)

```javascript
export const consignmentRentalOrderCategories = {
  consignmentRentalOrders: 1,           // 先租后买订单
  entitleServiceMonthlyOrders: 9,       // 权益服务订单（月付）
  entitleServiceDisposableOrders: 8,    // 权益服务订单（次付）
  vehicleDepositOrders: 25,             // 预付款订单
  mileageMarginOrders: 12,              // 里程保证金订单
  renewalMarginOrders: 14,              // 续保保证金订单
  repayMarginOrders: 15,                // 还款保证金订单
  returnCarOrders: 4,                   // 退车定损订单
  breachPenaltyOrders: 13,              // 解约违约金订单
  equityServiceOrders: 6,               // 权益卡订单
  violationOrders: 22,                  // 违章代办订单
  valueAddedServiceOrders: 20,          // 增值服务订单
  transferOwnershipOrders: 24,          // 过户订单
  balancePaymentOrders: 16,             // 尾款订单
  securityPenaltyOrders: 18,            // 安全违约金订单
  acceleAiscountFeeOrders: 7,           // 车辆加速折扣费订单
  returnCarVehicleOrders: 5,            // 退车车务订单
  mandatoryServiceChargeOrders: 10,     // 强制收车服务费订单
}

export const getConsignmentRentalOrderCategory = (orderType) => {
  return consignmentRentalOrderCategories[orderType] || 1
}
```

### 2. 车辆租赁 (carRental)

```javascript
export const carRentalOrderCategories = {
  conventLeaseOrders: 1,                // 常规租赁订单
  returnCarOrders: 4,                   // 退车定损订单
  returnCarVehicleOrders: 5,            // 退车车务订单
  equityServiceOrders: 6,               // 权益卡订单
  acceleAiscountFeeOrders: 7,           // 车辆加速折扣费订单
  entitleServiceOrders: 9,              // 权益服务订单
  mandatoryServiceChargeOrders: 10,     // 强制收车服务费订单
  illegalMarginOrders: 11,              // 违法保证金订单
  earlyReturnPenaltyOrders: 13,         // 早退违约金订单
  replaceCarOrders: 17,                 // 替换车订单
  securityPenaltyOrders: 18,            // 安全违约金订单
  valueAddedServiceOrders: 20,          // 增值服务订单
  safeDepositOrders: 21,                // 安全保证金订单
  violationOrders: 22,                  // 违章代办订单
  vehicleDepositOrders: 25,             // 预付款订单
}

export const getCarRentalOrderCategory = (orderType) => {
  return carRentalOrderCategories[orderType] || 1
}
```

### 3. 欢乐租 (happlyRental)

```javascript
export const happlyRentalOrderCategories = {
  returnCarOrders: 4,                   // 退车定损订单
  marginOrders: 12,                     // 保证金订单
  breachPenaltyOrders: 13,              // 解约违约金订单
  balancePaymentOrders: 16,             // 尾款订单
  securityPenaltyOrders: 18,            // 安全违约金订单
  valueAddedServiceOrders: 20,          // 增值服务订单
  violationOrders: 22,                  // 违章代办订单
  monthlyRentOrders: 23,                // 月租订单
  entitleServiceDisposableOrders: 8,    // 权益服务订单（次付）
  entitleServiceMonthlyOrders: 9,       // 权益服务订单（月付）
  mandatoryServiceChargeOrders: 10,     // 强制收车服务费订单
  preGoldOrders: 11,                    // 预付金订单
  fristServiceOrders: 25,               // 首服务订单
}

export const getHapplyRentalOrderCategory = (orderType) => {
  return happlyRentalOrderCategories[orderType] || 4
}
```

### 4. 权益服务 (equityServices)

```javascript
export const equityServicesOrderCategories = {
  equityServiceOrders: 6,               // 权益服务订单
  servicePerformOrders: 19,             // 服务履约订单
  valueAddedServiceOrders: 20,          // 增值服务订单
}

export const getEquityServicesOrderCategory = (orderType) => {
  return equityServicesOrderCategories[orderType] || 6
}
```

### 5. 代收款 (escrowCollect)

```javascript
export const escrowCollectOrderCategories = {
  behalfCollectInsuranceOrders: 20,     // 代收保险订单
  entrustPreSaleOrders: 26,             // 委托预售订单
  financialMonthlyRentOrders: 27,       // 金融月租订单
}

export const getEscrowCollectOrderCategory = (orderType) => {
  return escrowCollectOrderCategories[orderType] || 20
}
```

## 🔧 使用方式

### 导入和使用

```javascript
// 根据业务模块导入对应的函数
import { getConsignmentRentalOrderCategory } from '@/utils/system/constant'
import { getCarRentalOrderCategory } from '@/utils/system/constant'
import { getHapplyRentalOrderCategory } from '@/utils/system/constant'
import { getEquityServicesOrderCategory } from '@/utils/system/constant'
import { getEscrowCollectOrderCategory } from '@/utils/system/constant'

// 在 getParams 方法中使用
getParams() {
  return {
    ...getParams({
      data: params,
      queryItems: [1, 2, 3, 5],
      orderCategory: getConsignmentRentalOrderCategory('mileageMarginOrders'),
      orderBusinessType: 'rent_for_sale',
    })
  }
}
```

## ✅ 已完成的改造

### consignmentRental 模块
- ✅ 大部分订单类型的 index.vue 和 detail.vue 已完成改造

### carRental 模块
- ✅ conventLeaseOrders/index.vue
- ✅ safeDepositOrders/index.vue

### happlyRental 模块
- ✅ returnCarOrders/index.vue
- ✅ violationOrders/index.vue
- ✅ valueAddedServiceOrders/index.vue

### escrowCollect 模块
- ✅ behalfCollectInsuranceOrders/index.vue

## 🎯 优势

1. **统一管理**：所有业务模块的 orderCategory 配置集中管理
2. **避免重复**：index.vue 和 detail.vue 使用相同的配置源
3. **易于维护**：修改 orderCategory 只需要在配置文件中修改一处
4. **业务隔离**：不同业务模块使用独立的配置和工具函数
5. **类型安全**：通过工具函数提供更好的错误处理和默认值

## 📝 后续工作

继续按照相同模式改造剩余的订单类型文件，特别是各个业务模块的 detail.vue 文件。
