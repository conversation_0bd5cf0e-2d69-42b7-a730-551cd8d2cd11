<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <DstFiltrate ref="DstFiltrate" :data-arr="setFiltrateData" @searchClick="searchClick"></DstFiltrate>
    <div class="dst-table-content">
      <sys-table
        local-key="carRental_vehicleDepositOrders"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :is-loading="isLock"
        @sizeChange="sizeChange"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { getEntrustPreSaleOrderList } from '@/api/escrowCollect/entrustPreSaleOrders'
import { changeVariableName } from '@/utils/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
export default {
  name: 'OldEscrowCollectEntrustPreSaleOrders',
  mixins: [mixin, orderMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 增值服务订单总数
      paginationTotal: 0,
      // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
      type: 5,
    }
  },
  computed: {
    // 增值服务订单搜素的配置项
    setFiltrateData,
    // 增值服务订单列表表头项
    setTableHeadArr,
  },
  methods: {
    /**
     * @description:请求增值服务订单列表数据
     * @param {boolean} 是否需要loading
     * @return:
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      getEntrustPreSaleOrderList(data)
        .then((res) => {
          this.paginationTotal = res.data.totalNum
          this.tableList = res.data.list
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
        orderTypeList: [26],
      }
      changeVariableName(params, 'createTime', true, 'Time')
      params.priceStatus = params.priceStatus
        ? Number(params.priceStatus)
        : params.priceStatus
      params.carNoList = this.$toggleSplitJoin(params, 'carNoList') || []
      this.getCustomerIds(params, false)
      this.searchParams = _.cloneDeep(params)
      return params
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/.label-left {
  margin-right: 10px;
}
</style>
