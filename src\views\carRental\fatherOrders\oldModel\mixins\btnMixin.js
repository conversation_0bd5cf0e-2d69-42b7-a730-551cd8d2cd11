import { getParentOrderSubList } from '@/api/common/rental/parent'
// import { getStoreList } from '@/api/common/store'
// import { getStationList } from '@/api/common/station'
import { getSimpleDictByType } from '@/api/base'
import dictionaries from '@/utils/common.js'

export default {
  computed: {
    // 按钮数据
    btnList() {
      return [
        {
          show: true && false,
          click: this.cancelOrder,
          text: '取消订单',
        },
        // carStatus  车状态，1待支付，2待交车，3交车中，4已交车，5还车中，6已还车
        {
          show: this.baseInfoForm && this.baseInfoForm.carStatus === 2 && false,
          click: this.handCar,
          text: '发起交车',
        },
        {
          show: this.baseInfoForm && this.baseInfoForm.carStatus === 3 && false,
          click: this.invalidCar,
          text: '作废交车',
        },
        {
          show: this.baseInfoForm && this.baseInfoForm.carStatus === 4 && false,
          click: this.returnCar,
          text: '发起还车',
        },
        {
          show: this.baseInfoForm && this.baseInfoForm.carStatus === 5 && false,
          click: this.invalidReturnCar,
          text: '作废还车',
        },
        {
          show: true && false,
          click: this.backCar,
          text: '确定退车',
        },
        {
          show: false,
          click: this.relet,
          text: '续租/转租赁',
        },
        {
          show: true && false,
          click: this.sublet,
          text: '替换车转租赁',
        },
        {
          show: true && false,
          click: this.transferCar,
          text: '发起换车',
        },
        {
          show: true && false,
          click: this.transferCar2,
          text: '发起换车',
        },
        {
          show: true && false,
          click: this.invalidTransferCar,
          text: '作废换车',
        },
        {
          show: true && false,
          click: this.abnormalCar,
          text: '异常交车',
        },
        {
          show: true && false,
          click: this.handleRefund,
          text: '发起退款',
        },
        {
          show: false,
          click: this.payOvertime,
          text: '支付超时',
        },
        {
          show: false,
          click: this.paymentSuccess,
          text: '支付成功',
        },
        {
          show: false,
          click: this.payCarFinish,
          text: '交车完成',
        },
        {
          show: false,
          click: this.completeReturnCar,
          text: '还车完成',
        },
        {
          show: false,
          click: this.returnCar2,
          text: '发起还车',
        },
        {
          show: false,
          click: this.clearArrears,
          text: '结算欠款',
        },
        {
          show: false,
          click: this.clearArrears2,
          text: '结算欠款30天后',
        },
        {
          show: false,
          click: this.createIllegalOrder,
          text: '创建违章代办订单',
        },
        {
          show: false,
          click: this.completeIllegalOrder,
          text: '违章代办订单完成',
        },
        {
          show: false,
          click: this.cancelIllegalOrder,
          text: '违章代办订单取消',
        },
        {
          show: false,
          click: this.createSecurityPenaltyOrder,
          text: '创建安全违约金订单',
        },
        {
          show: false,
          click: this.cancelSecurityPenaltyOrder,
          text: '安全违约金订单取消',
        },
        {
          show: false,
          click: this.createVehicleAmountOrder,
          text: '创建车辆加速抵扣费订单',
        },
        {
          show: false,
          click: this.cancelVehicleAmountOrder,
          text: '车辆加速抵扣费订单取消',
        },
        {
          show: false,
          click: this.forAdvance,
          text: '交车完成（提前提车）',
        },
        {
          show: false,
          click: this.delayCar,
          text: '还车完成（延后还车）',
        },
      ]
    },
  },
  methods: {
    /**
     * @description:取消订单
     * @param {type}
     * @return:
     */
    cancelOrder() {
      this.componentKey = 'CancelOrder'
    },
    /**
     * @description:发起交车
     * @param {type}
     * @return:
     */
    handCar() {
      this.isLock = true
      getParentOrderSubList({}, 1)
        .then(async () => {
          this.isLock = false
          this.componentKey = 'HandCar'
        })
        .catch(() => {
          setTimeout(() => {
            this.isLock = false
            this.componentKey = 'HandCar'
          }, 1000)
        })
    },
    /**
     * @description:作废交车
     * @param {type}
     * @return:
     */
    invalidCar() {
      this.componentKey = 'InvalidCar'
    },
    /**
     * @description:发起还车
     * @param {type}
     * @return:
     */
    returnCar() {
      this.isLock = true
      getParentOrderSubList({}, 1)
        .then(async () => {
          this.isLock = false
          this.componentKey = 'ReturnCar'
        })
        .catch(() => {
          setTimeout(() => {
            this.isLock = false
            this.componentKey = 'ReturnCar'
          }, 1000)
        })
    },
    /**
     * @description:作废还车
     * @param {type}
     * @return:
     */
    invalidReturnCar() {
      this.componentKey = 'InvalidReturnCar'
    },
    /**
     * @description:确定退车
     * @param {type}
     * @return:
     */
    backCar() {
      this.componentKey = 'BackCar'
    },
    /**
     * @description:续租
     * @param {type}
     * @return:
     */
    relet() {
      this.componentKey = 'Relet'
    },
    /**
     * @description:替换车转租赁
     * @param {type}
     * @return:
     */
    sublet() {
      this.componentKey = 'Sublet'
    },
    /**
     * @description:发起换车
     * @param {type}
     * @return:
     */
    transferCar() {
      this.isLock = true
      getParentOrderSubList({}, 1)
        .then(async () => {
          Promise.all([
            getSimpleDictByType({ type: dictionaries['replaceCarOrderType'] }),
            // getStoreList()
          ])
            .then(async (res) => {
              if (res && res.length) {
                this.transferCarReason = res[0].data
                // this.storeMainte = res[1].data.list
              }
            })
            .finally(() => {
              this.componentKey = 'TransferCar'
              this.isLock = false
            })
        })
        .catch(() => {
          setTimeout(() => {
            this.componentKey = 'TransferCar'
            this.isLock = false
          }, 1000)
        })
    },
    /**
     * @description:发起换车
     * @param {type}
     * @return:
     */
    transferCar2() {
      this.isLock = true
      // getStoreList().then(async (res) => {
      //   if (res && res.length) {
      //     this.storeMainte = res.data.list
      //   }
      //   this.componentKey = 'TransferCar2'
      //   this.isLock = false
      // })
      //   .catch(() => {
      //     this.isLock = false
      //   })
      setTimeout(() => {
        this.componentKey = 'TransferCar2'
        this.isLock = false
      }, 1000)
    },
    /**
     * @description:作废换车
     * @param {type}
     * @return:
     */
    invalidTransferCar() {
      this.componentKey = 'InvalidTransferCar'
    },
    /**
     * @description:异常交车
     * @param {type}
     * @return:
     */
    abnormalCar() {
      this.abnormalDeliveryFlag = true
    },
    /**
     * @description:发起退款
     * @param {type}
     * @return:
     */
    handleRefund() {
      this.$router.push({
        path: '/carRental/fatherOrders/handleRefund',
        query: {
          code: this.parentCode,
        },
      })
    },
    /**
     * @description:支付超时
     * @param {type}
     * @return:
     */
    payOvertime() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('支付超时')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:支付成功
     * @param {type}
     * @return:
     */
    paymentSuccess() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('支付成功')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:交车完成
     * @param {type}
     * @return:
     */
    payCarFinish() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('交车完成')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:还车完成
     * @param {type}
     * @return:
     */
    completeReturnCar() {
      this.completeReturnCarFlag = true
    },
    /**
     * @description:发起还车
     * @param {type}
     * @return:
     */
    returnCar2() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('发起还车')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:结算欠款
     * @param {type}
     * @return:
     */
    clearArrears() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('结算欠款')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:结算欠款30天后
     * @param {type}
     * @return:
     */
    clearArrears2() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('结算欠款30天后')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:创建违章代办订单
     * @param {type}
     * @return:
     */
    createIllegalOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('创建违章代办订单')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:违章代办订单完成
     * @param {type}
     * @return:
     */
    completeIllegalOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('违章代办订单完成')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:违章代办订单取消
     * @param {type}
     * @return:
     */
    cancelIllegalOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('违章代办订单取消')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:创建安全违约金订单
     * @param {type}
     * @return:
     */
    createSecurityPenaltyOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('创建安全违约金订单')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:安全违约金订单取消
     * @param {type}
     * @return:
     */
    cancelSecurityPenaltyOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('安全违约金订单取消')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:创建车辆加速抵扣费订单
     * @param {type}
     * @return:
     */
    createVehicleAmountOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('创建车辆加速抵扣费订单')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:车辆加速抵扣费订单取消
     * @param {type}
     * @return:
     */
    cancelVehicleAmountOrder() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('车辆加速抵扣费订单取消')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:提前提车
     * @param {type}
     * @return:
     */
    forAdvance() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('提前提车成功')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
    /**
     * @description:延后还车
     * @param {type}
     * @return:
     */
    delayCar() {
      this.isLock = true
      getParentOrderSubList()
        .then(async (res) => {
          this.$message.success('延后还车成功')
          this.refresh()
        })
        .catch(() => {
          this.isLock = false
        })
    },
  },
}
