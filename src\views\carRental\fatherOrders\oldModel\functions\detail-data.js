/* eslint-disable jsx-quotes */
import {
  rangeNumber,
  checkSelect,
  checkTitleLenth,
} from '@/portal-common/utils/testedFunc/formValidate'
import { showData } from '@/utils/business'
import common from '@/utils/common.js'

/**
 * @description:完成退车配置项
 * @param {type}
 * @return:
 */
export function detailDialogDataArr() {
  return [
    {
      prop: 'lossCategory',
      label: '定损项目商品类目对应',
      type: 'radio',
      class: 'dst-block',
      labelWidth: '180px',
      options: [
        { label: '退车定损订单', value: 1 },
        { label: '退车车务订单', value: 2 },
      ],
      rules: [{ required: true, trigger: ['change', 'blur'] }],
    },
  ]
}

/**
 * @description:基础信息
 * @param {type}
 * @return:
 */
export function baseInfoConfig(v, key) {
  const result = [
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'createTime',
      label: '创建时间',
      type: 'text',
    },
    {
      prop: 'payEndTime',
      label: '支付截止时间',
      type: 'text',
    },
    {
      prop: 'carUseName',
      label: '车辆用途',
      type: 'text',
    },
    {
      prop: 'isBondName',
      label: '保证金',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'performTypeName',
      label: '履约条件',
      type: 'text',
    },
    {
      prop: 'cancelTime',
      label: '取消时间',
      type: 'text',
    },
    {
      prop: 'orderSourceName',
      label: '下单渠道',
      type: 'text',
    },
    {
      prop: 'orderChannel',
      label: '订单渠道',
      type: 'text',
    },
    {
      prop: 'cityName',
      label: '城市',
      type: 'text',
    },
    {
      prop: 'subName',
      label: '集团归属',
      type: 'text',
    },
    {
      prop: 'regionName',
      label: '大区归属',
      type: 'text',
    },
    {
      prop: 'orgName',
      label: '运营组织',
      type: 'text',
    },
    {
      prop: 'serviceOrgCityName',
      label: '服务组织',
      type: 'text',
    },
    {
      prop: 'marketingOrgName',
      label: '营销组织',
      type: 'text',
    },
    {
      prop: 'customerGroupTypeName',
      label: '业务通路',
      type: 'text',
    },
    {
      prop: 'carNo',
      label: '车牌号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'vinCode',
      label: '车架号',
      type: 'text',
      class: 'font-bold',
    },
    {
      prop: 'expDeliveryTime',
      label: '预计交车时间',
      type: 'text',
    },
    {
      prop: 'expBackCarTime',
      label: '预计履约结束日期',
      type: 'text',
    },
    {
      prop: 'expireTime',
      label: '订单有效期',
      type: 'render',
      class: 'font-bold',
      render: (h, ctx) => {
        const text = showData({
          data: [v.baseInfoForm.expireStartTime, v.baseInfoForm.expireEndTime],
          showEmpty: true,
          spliter: '~',
          type: 'time',
          strict: false,
        })
        return text !== '-' ? (
          <el-tooltip effect="dark" content={text} placement="top">
            <div class="font-bold font-text">{text}</div>
          </el-tooltip>
        ) : (
          <span> - </span>
        )
      },
    },
    // {
    //   prop: 'performTime',
    //   label: '订单履约时间',
    //   type: 'render',
    //   class: 'font-bold',
    //   render: (h, ctx) => {
    //     const text = showData({
    //       data: [
    //         v.baseInfoForm.performStartTime,
    //         v.baseInfoForm.performEndTime,
    //       ],
    //       showEmpty: true,
    //       spliter: '~',
    //       type: 'time',
    //       strict: false,
    //     })
    //     return text !== '-' ? (
    //       <el-tooltip effect="dark" content={text} placement="top">
    //         <div class="font-bold font-text">{text}</div>
    //       </el-tooltip>
    //     ) : (
    //       <span> - </span>
    //     )
    //   },
    // },
    {
      prop: 'expLeaseMonth',
      label: '预计租赁时长（月）',
      type: 'text',
    },
    {
      prop: 'realLeaseMonth',
      label: '实际租赁时长（月）',
      type: 'text',
    },
    {
      prop: 'violationAuditStatusName',
      label: '违章审核状态',
      type: 'text',
    },
    {
      prop: 'lossScenePaymentName',
      label: '定损支付方',
      type: 'text',
    },
    {
      prop: 'billingFulfillMethodName',
      label: '履约计费方式',
      type: 'text',
    },
    // {
    //   prop: 'expAmount',
    //   label: '预计收入',
    //   type: 'render',
    //   render: (h, item, parent, { form }) => {
    //     return (
    //       <span>
    //         {showData({ data: [v.baseInfoForm.expAmount], frontWord: '￥' })}
    //       </span>
    //     )
    //   },
    // },
    // {
    //   prop: 'realAmount',
    //   label: '已确认收入',
    //   type: 'render',
    //   render: (h, item, parent, { form }) => {
    //     return (
    //       <span>
    //         {showData({ data: [v.baseInfoForm.realAmount], frontWord: '￥' })}
    //       </span>
    //     )
    //   },
    // },
  ]
  if (v.baseInfoForm.rentType == 1) {
    result.push({
      prop: 'isPaidName',
      label: '是否有偿替换车',
      type: 'text',
    })
  }
  result.push(
    {
      prop: 'orderRecommenderInfo',
      label: '推荐人',
      type: 'slot',
      class: 'dst-block order-recommender-info',
    },
    {
      prop: 'orderSalemansInfo',
      label: '业务归属',
      type: 'slot',
      class: 'dst-block order-saleman-info',
    }
  )
  return result
}

/**
 * @description:续租弹框表单配置项
 * @param {type}
 * @return:
 */
export function reletDataArr() {
  const v = this
  return [
    {
      prop: 'reletTime',
      label: '续租时长（月）',
      type: 'render',
      class: 'dst-block',
      labelWidth: '180px',
      rules: [
        {
          required: true,
          validator: rangeNumber({ min: 1, max: 12 }),
          trigger: ['blur', 'change'],
        },
      ],
      render: (h, item, parent, { form }) => {
        return (
          <el-input-number
            v-model={v.ruleData.reletTime}
            min={1}
            max={12}
            label="续租时长（月）"
          ></el-input-number>
        )
      },
    },
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'input',
      class: 'dst-block',
      labelWidth: '180px',
      rules: [{ required: true, trigger: ['change', 'blur'] }],
    },
  ]
}

/**
 * @description:替换车转租赁弹框表单配置项
 * @param {type}
 * @return:
 */
export function subletDataArr() {
  const v = this
  return [
    {
      prop: 'subletTime',
      label: '租赁时长（月）',
      type: 'render',
      class: 'dst-block',
      labelWidth: '180px',
      rules: [
        {
          required: true,
          validator: rangeNumber({ min: 1, max: 12 }),
          trigger: ['blur', 'change'],
        },
      ],
      render: (h, item, parent, { form }) => {
        return (
          <el-input-number
            v-model={v.ruleData.subletTime}
            min={1}
            max={12}
            label="租赁时长（月）"
          ></el-input-number>
        )
      },
    },
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'input',
      class: 'dst-block',
      labelWidth: '180px',
      rules: [{ required: true, trigger: ['change', 'blur'] }],
    },
  ]
}

/**
 * @description:发起换车弹框表单配置项
 * @param {type}
 * @return:
 */
export function transferCarDialogConfig() {
  return [
    {
      prop: 'reasonId',
      type: 'select',
      class: 'dst-block',
      label: '换车原因',
      options: this.transferCarReason,
      props: {
        label: 'name',
        value: 'id',
      },
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择换车原因',
          }),
          trigger: ['change', 'blur'],
        },
      ],
      change: (e, items) => {
        if (!items && !items[0]) return
        const item = items[0]
        this.$set(this.ruleData, 'reason', item.name)
      },
    },
    {
      prop: 'replaceCar',
      label: '替换车',
      type: 'render',
      class: 'bold-font',
      render: (h, item, parent, { form }) => {
        return <span />
      },
    },
    {
      label: '交车地点',
      prop: 'stationId',
      class: 'dst-block',
      type: 'slot',
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择交车地点',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'expDeliveryTime',
      label: '预计交车日期',
      type: 'date',
      class: 'dst-block',
      labelWidth: '120px',
      rules: [
        {
          required: true,
          validator: checkSelect(),
          trigger: ['change', 'blur'],
        },
      ],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        },
      },
    },
    {
      prop: 'deliveryMethod',
      label: '交车方式',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '客户到场站自提', value: 1 },
        { label: '送到客户指定地点', value: 2 },
      ],
      rules: [
        {
          required: false,
          validator: checkSelect({
            required: false,
            customMessage: '请选择交车方式',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'originalCar',
      label: '原车',
      type: 'render',
      class: 'bold-font',
      render: (h, item, parent, { form }) => {
        return <span />
      },
    },
    {
      prop: 'maintenanceStore',
      type: 'select',
      class: 'dst-block',
      label: '维保门店',
      options: this.storeMainte,
      props: {
        label: 'store_name',
        value: 'store_id',
      },
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择维保门店',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'expBackCarTime',
      label: '预计还车日期',
      type: 'date',
      class: 'dst-block',
      labelWidth: '120px',
      rules: [
        {
          required: true,
          validator: checkSelect(),
          trigger: ['change', 'blur'],
        },
      ],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        },
      },
    },
    {
      prop: 'remark',
      label: '备注',
      type: 'textarea',
      class: 'dst-block',
      rules: [
        {
          required: false,
          validator: checkTitleLenth({ required: false, max: 200 }),
          trigger: ['change', 'blur'],
        },
      ],
    },
  ]
}

/**
 * @description:发起换车弹框表单配置项
 * @param {type}
 * @return:
 */
export function transferCarDialogConfig2() {
  return [
    {
      prop: 'originalCar',
      label: '原车',
      type: 'render',
      class: 'bold-font',
      render: (h, item, parent, { form }) => {
        return <span />
      },
    },
    {
      prop: 'maintenanceStore',
      type: 'select',
      class: 'dst-block',
      label: '维保门店',
      options: this.storeMainte,
      props: {
        label: 'store_name',
        value: 'store_id',
      },
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择维保门店',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'expDeliveryTime',
      label: '预计交车日期',
      type: 'date',
      class: 'dst-block',
      labelWidth: '120px',
      rules: [
        {
          required: true,
          validator: checkSelect(),
          trigger: ['change', 'blur'],
        },
      ],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        },
      },
    },
    {
      prop: 'replaceCar',
      label: '替换车',
      type: 'render',
      class: 'bold-font',
      render: (h, item, parent, { form }) => {
        return <span />
      },
    },
    {
      label: '还车地点',
      prop: 'stationId',
      class: 'dst-block',
      type: 'slot',
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择还车地点',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'expBackCarTime',
      label: '预计还车日期',
      type: 'date',
      class: 'dst-block',
      labelWidth: '120px',
      rules: [
        {
          required: true,
          validator: checkSelect(),
          trigger: ['change', 'blur'],
        },
      ],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        },
      },
    },
    {
      prop: 'backCarWay',
      label: '还车方式',
      type: 'radio',
      class: 'dst-block',
      options: [
        { label: '客户送到场站', value: 1 },
        { label: '到客户指定地点取车', value: 2 },
      ],
      rules: [
        {
          required: false,
          validator: checkSelect({
            required: false,
            customMessage: '请选择还车方式',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'remark',
      label: '备注',
      type: 'textarea',
      class: 'dst-block',
      rules: [
        {
          required: false,
          validator: checkTitleLenth({ required: false, max: 200 }),
          trigger: ['change', 'blur'],
        },
      ],
    },
  ]
}

/**
 * @description:作废换车弹框表单配置项
 * @param {type}
 * @return:
 */
export function invalidTransferCarDialogConfig() {
  return [
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'input',
      class: 'dst-block',
      labelWidth: '120px',
      rules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (value) {
              if (value !== this.parentCode) {
                return callback(new Error('请填写正确的订单编号'))
              }
              return callback()
            } else {
              return callback(new Error('订单编号不能为空'))
            }
          },
          trigger: ['change', 'blur'],
        },
      ],
    },
  ]
}

/**
 * @description:异常交车基础信息
 * @param {type}
 * @return:
 */
export function abnormalDeliveryBaseInfoConfig() {
  return [
    {
      prop: 'customerName',
      label: '客户名称',
      type: 'text',
    },
    {
      prop: 'customerClassName',
      label: '客户类群',
      type: 'text',
    },
    {
      prop: 'subName',
      label: '归属集团',
      type: 'text',
    },
    // {
    //   prop: 'customerLevelName',
    //   label: '客户级别',
    //   type: 'text',
    // },
    {
      prop: 'regionName',
      label: '所属大区',
      type: 'text',
    },
    {
      prop: 'orderCode',
      label: '订单编号',
      type: 'text',
    },
  ]
}

/**
 * @description:异常交车商品信息（应交商品信息）
 * @param {type}
 * @return:
 */
export function abnormalDeliveryGoodSubmitInfoConfig() {
  return [
    {
      prop: 'title',
      label: '应交商品信息',
      type: 'render',
      labelWidth: '140px',
      class: 'dst-block bold-font',
      render: (h, item, parent, { form }) => {
        return <span />
      },
    },
    {
      prop: 'locationCityName',
      label: '提车城市',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'goodsName',
      label: '商品名称',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'goodsSaleName',
      label: '销售规格',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'goodsPrice',
      label: '商品价格',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'leaseAverageAmount',
      label: '车辆租金/月',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'serviceAverageAmount',
      label: '基础服务费/月',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'addServicePrice',
      label: '增值服务费/月',
      type: 'text',
      class: 'dst-block',
    },
  ]
}

/**
 * @description:异常交车商品信息（实交商品信息）
 * @param {type}
 * @return:
 */
export function abnormalDeliveryGoodPayInfoConfig() {
  return [
    {
      prop: 'title',
      label: '实交商品信息',
      type: 'render',
      labelWidth: '140px',
      class: 'dst-block bold-font',
      render: (h, item, parent, { form }) => {
        return <span />
      },
    },
    {
      prop: 'locationCityName',
      label: '提车城市',
      type: 'text',
      class: 'dst-block',
    },
    {
      label: '商品名称',
      prop: 'selectGoodsName',
      class: 'dst-block',
      type: 'slot',
      isHidden: this.goodPracticalInfoForm.selectGoodsName,
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择商品名称',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      label: '商品名称',
      prop: 'selectGoodsName',
      class: 'dst-block',
      type: 'text',
      isHidden: !this.goodPracticalInfoForm.selectGoodsName,
    },
    {
      prop: 'selectGoodsSalesName',
      label: '销售规格',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'selectRentPrice',
      label: '商品价格',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'selectCarRent',
      label: '车辆租金/月',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'selectServicePrice',
      label: '基础服务费/月',
      type: 'text',
      class: 'dst-block',
    },
    {
      prop: 'selectAddServicePrice',
      label: '增值服务费/月',
      type: 'text',
      class: 'dst-block',
    },
  ]
}

/**
 * @description:异常交车原因表单配置
 * @param {type}
 * @return:
 */
export function abnormalDeliveryReasonConfig() {
  return [
    {
      prop: 'dingId',
      type: 'select',
      label: '申请人',
      filterable: true,
      remote: true,
      reservekeyword: true,
      clearable: true,
      remoteMethod: this.getSaleman,
      options: this.dingIdOption,
      class: 'dst-block',
      props: {
        label: 'name',
        value: 'dd_user_id',
      },
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择申请人',
            minSelectNum: null,
            maxSelectNum: null,
          }),
          trigger: 'change',
        },
      ],
      change: (e, item) => {
        if (e) {
          this.reasonForm.saleName = item[0].name
        } else {
          this.reasonForm.saleName = null
        }
      },
    },
    {
      prop: 'goodsChangeReason',
      type: 'select',
      label: '异常交车原因',
      options: this.reasonOption,
      class: 'dst-block',
      props: {
        label: 'name',
        value: 'id',
      },
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '',
            minSelectNum: null,
            maxSelectNum: null,
          }),
          trigger: 'change',
        },
      ],
      change: (e, item) => {
        this.$refs.station.clean()
        this.$set(this.reasonForm, 'planStationId', null)
        this.$set(this.reasonForm, 'planStationName', null)
        this.$nextTick(() => {
          const dstForm = this.$refs.ruleForm
          if (dstForm.$children && dstForm.$children.length) {
            const children = dstForm.$children[0]
            children.clearValidate('planStationId')
          }
        })
        if (e) {
          let { name } = item[0]
          this.reasonForm.goodsChangeReasonName = name
          return
        }
        this.reasonForm.goodsChangeReasonName = null
      },
    },
    {
      label: '交付场站',
      prop: 'planStationId',
      class: 'dst-block',
      type: 'slot',
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请选择交付场站',
          }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'remark',
      label: '备注',
      type: 'textarea',
      class: 'dst-block',
      rules: [
        {
          required: false,
          validator: checkTitleLenth({ required: false, max: 200 }),
          trigger: ['change', 'blur'],
        },
      ],
    },
    {
      prop: 'attachmentUrls',
      type: 'dstUpload',
      label: '附件',
      actionUrl: common.operationUrl + '/api/oss/uploadFile',
      baseApi: process.env.BASE_API,
      rules: [
        {
          required: true,
          validator: checkSelect({
            required: true,
            customMessage: '请上传附件',
            minSelectNum: null,
            maxSelectNum: null,
          }),
          trigger: 'change',
        },
      ],
    },
  ]
}
