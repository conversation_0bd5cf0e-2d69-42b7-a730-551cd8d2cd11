<template>
  <sys-card title="基础信息" slot-name="baseInfo" v-loading="isLock">
    <template slot="baseInfo_body">
      <template>
        <dstForm
          ref="ruleForm"
          label-width="140px"
          mode-type="detail"
          v-model="baseInfoForm"
          :data-arr="baseInfoConfig"
        >
          <template slot="orderRecommenderInfo">
            <recommend-info :data="baseInfoForm.orderRecommender || {}" />
          </template>
          <template slot="orderSalemansInfo">
            <saleman-info :list="baseInfoForm.parentOrderSalemans" />
          </template>
        </dstForm>
      </template>
    </template>
  </sys-card>
</template>

<script>
/* eslint-disable jsx-quotes */
import { baseInfo1, baseInfo2 } from '../functions/detail-data'
import contractMixins from '@/views/common/common/mixins/contractMixins.js'
import RecommendInfo from '@/views/common/parentOrder/components/baseInfo/RecommendInfo.vue'
import SalemanInfo from '@/views/common/parentOrder/components/baseInfo/SalemanInfo.vue'
import { isEmpty } from '@/utils'
export default {
  name: 'BaseInfoDetail',
  components: { RecommendInfo, SalemanInfo },
  mixins: [contractMixins],
  props: {
    // 父订单基础信息
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  computed: {
    // 基础信息展示项处理
    baseInfoConfig() {
      const baseData1 = baseInfo1(this, 'baseInfo')
      const baseData2 = baseInfo2(this, 'baseInfo')
      let contractCodeList = this.getData()
      return [...baseData1, ...contractCodeList, ...baseData2]
    },
  },
  methods: {
    /**
     * @description:配置合同编码数据展示
     * @param {string} data 获取取值字段
     * @return:
     */
    getData() {
      let contractCodeInfo = []
      let contractCodeList = []
      if (!isEmpty(this.baseInfoForm.parentOrderContract)) {
        contractCodeList = [this.baseInfoForm.parentOrderContract]
      }
      if (!contractCodeList || contractCodeList.length <= 1) {
        let flag = contractCodeList && contractCodeList.length
        let contractCode,
          contractType,
          id,
          contractStyle,
          contractPartyName,
          contractPartaName
        if (flag) {
          contractCode = contractCodeList[0].contractCode
          contractType = contractCodeList[0].contractType
          contractStyle = contractCodeList[0].contractStyle
          id = contractCodeList[0].contractId
          contractPartyName = contractCodeList[0].contractPartyName
          contractPartaName = contractCodeList[0].contractPartaName
        }
        contractCodeInfo.push({
          prop: 'contractCode',
          label: '合同编码',
          type: 'render',
          class: flag ? 'font-bold' : 'none-data',
          render: (h, item, parent, { form }) => {
            if (flag) {
              return (
                <el-tooltip
                  effect="dark"
                  content={contractCode}
                  placement="top"
                >
                  <sys-link
                    underline={false}
                    onClick={() =>
                      this.getContractStyle({
                        type: contractType,
                        id,
                        code: contractCode,
                        style: contractStyle,
                      })
                    }
                  >
                    <div class="dst-add-input font-bold">{contractCode}</div>
                  </sys-link>
                </el-tooltip>
              )
            } else {
              return <span> - </span>
            }
          },
        })
        contractCodeInfo.push({
          prop: 'contractPartyName',
          label: '合同乙方',
          type: 'render',
          class: !isEmpty(contractPartyName) ? 'font-bold' : 'none-data',
          render: (h, formItem, parent, { form }) => {
            if (!isEmpty(contractPartyName)) {
              return (
                <el-tooltip
                  effect="dark"
                  content={contractPartyName}
                  placement="top"
                >
                  <div class="dst-add-input font-bold">{contractPartyName}</div>
                </el-tooltip>
              )
            } else {
              return <span> - </span>
            }
          },
        })
        contractCodeInfo.push({
          prop: 'contractPartaName',
          label: '合同甲方',
          type: 'render',
          class: !isEmpty(contractPartaName) ? 'font-bold' : 'none-data',
          render: (h, formItem, parent, { form }) => {
            if (!isEmpty(contractPartaName)) {
              return (
                <el-tooltip
                  effect="dark"
                  content={contractPartaName}
                  placement="top"
                >
                  <div class="dst-add-input font-bold">{contractPartaName}</div>
                </el-tooltip>
              )
            } else {
              return <span> - </span>
            }
          },
        })
      } else {
        let codeList = contractCodeList.map((item, subIndex) => {
          let showIndex = Number(subIndex) + 1
          const name = 'contractCode' + showIndex
          const { contractCode, contractType, id, contractStyle } = item
          return {
            prop: name,
            label: '合同编码' + showIndex,
            type: 'render',
            class: contractCode ? 'font-bold' : 'none-data',
            render: (h, item, parent, { form }) => {
              if (contractCode) {
                return (
                  <el-tooltip
                    effect="dark"
                    content={contractCode}
                    placement="top"
                  >
                    <sys-link
                      underline={false}
                      onClick={() =>
                        this.getContractStyle({
                          type: contractType,
                          id,
                          code: contractCode,
                          style: contractStyle,
                        })
                      }
                    >
                      <div class="dst-add-input font-bold">{contractCode}</div>
                    </sys-link>
                  </el-tooltip>
                )
              } else {
                return <span> - </span>
              }
            },
          }
        })
        let partBList = contractCodeList.map((item, subIndex) => {
          let showIndex = Number(subIndex) + 1
          return {
            prop: 'contractPartyName' + showIndex,
            label: '合同乙方' + showIndex,
            type: 'render',
            class: !isEmpty(item.contractPartyName) ? 'font-bold' : 'none-data',
            render: (h, formItem, parent, { form }) => {
              if (!isEmpty(item.contractPartyName)) {
                return (
                  <el-tooltip
                    effect="dark"
                    content={item.contractPartyName}
                    placement="top"
                  >
                    <div class="dst-add-input font-bold">
                      {item.contractPartyName}
                    </div>
                  </el-tooltip>
                )
              } else {
                return <span> - </span>
              }
            },
          }
        })
        let partAList = contractCodeList.map((item, subIndex) => {
          let showIndex = Number(subIndex) + 1
          return {
            prop: 'contractPartaName' + showIndex,
            label: '合同甲方' + showIndex,
            type: 'render',
            class: !isEmpty(item.contractPartaName) ? 'font-bold' : 'none-data',
            render: (h, formItem, parent, { form }) => {
              if (!isEmpty(item.contractPartaName)) {
                return (
                  <el-tooltip
                    effect="dark"
                    content={item.contractPartaName}
                    placement="top"
                  >
                    <div class="dst-add-input font-bold">
                      {item.contractPartaName}
                    </div>
                  </el-tooltip>
                )
              } else {
                return <span> - </span>
              }
            },
          }
        })
        partAList.forEach((element, elementIndex) => {
          contractCodeInfo.push(element)
          contractCodeInfo.push(partBList[elementIndex])
          contractCodeInfo.push(codeList[elementIndex])
        })
      }
      return contractCodeInfo
    },
  },
}
</script>

<style lang="scss" scoped>
/deep/ .el-card__body {
  max-height: inherit !important;
  .order-recommender-info,
  .order-saleman-info {
    width: 100%;
  }
}
</style>
