<template>
  <div>
    <!-- 基础信息 -->
    <base-info-detail
      ref="baseInfo"
      :base-info-form="baseInfoForm"
      v-bind="$attrs"
      v-loading="isLock"
    ></base-info-detail>
    <!-- 客户信息 -->
    <customer-info
      ref="customerInfo"
      :type="type"
      :base-info-form="baseInfoForm"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="isLock"
    ></customer-info>
    <!-- 分期信息 -->
    <period-info
      v-if="!$isEmpty(baseInfoForm.instalmentVO)"
      ref="periodInfo"
      :type="type"
      :base-info-form="baseInfoForm"
      v-bind="$attrs"
      v-on="$listeners"
    ></period-info>
    <!-- 结算信息 -->
    <bill-info
      ref="billInfo"
      :type="type"
      :base-info-form="baseInfoForm"
      v-bind="$attrs"
      v-on="$listeners"
    ></bill-info>
    <!-- 管理备注 -->
    <management-note
      :type="type"
      :base-info-form="baseInfoForm"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="isLock"
    ></management-note>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import BaseInfoDetail from '../components/BaseInfo.vue'
import orderMixins from '@/views/common/common/mixins/orderLink'
import CustomerInfo from '@/views/common/parentOrder/components/customerInfo'
import ManagementNote from '@/views/common/parentOrder/components/managementNote'
import BillInfo from '@/views/common/childOrders/detailTableList/BillInfo'
import PeriodInfo from '../components//PeriodInfo'
export default {
  name: 'BaseInfo',
  components: {
    BaseInfoDetail,
    CustomerInfo,
    ManagementNote,
    BillInfo,
    PeriodInfo,
  },
  mixins: [orderMixins],
  props: {
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type: {
      type: [Number, String],
      default: '2',
      required: true,
    },
    // 当前主订单详情数据
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  data() {
    return {
      // loading加载
      isLock: false,
    }
  },
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
