<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <DstFiltrate ref="DstFiltrate" :data-arr="setFiltrateData" @searchClick="searchClick"></DstFiltrate>
    <div class="dst-table-content">
      <sys-table
        local-key="carRental_violationOrders"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :is-loading="isLock"
        @sizeChange="sizeChange"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/portal-common/utils/mixin/tableList.js'
import { getViolationOrderList } from '@/api/carRental/violationOrders'
import { changeVariableName } from '@/utils/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
import orderStatusMixins from '@/views/common/childOrders/mixins/orderStatus.js'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
export default {
  name: 'OldCarRentalViolationOrders',
  mixins: [mixin, orderMixins, orderStatusMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 违章代办订单总数
      paginationTotal: 0,
      // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
      type: 0,
    }
  },
  computed: {
    // 违章代办订单搜素的配置项
    setFiltrateData,
    // 违章代办订单列表表头项
    setTableHeadArr,
  },
  methods: {
    /**
     * @description:请求违章代办订单列表数据
     * @param {boolean} 是否需要loading
     * @return:
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      getViolationOrderList(data)
        .then((res) => {
          this.paginationTotal = res.data.totalNum
          this.tableList = res.data.list
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
      }
      changeVariableName(params, 'createTime', true, 'Time')
      params.priceStatus = params.priceStatus
        ? Number(params.priceStatus)
        : params.priceStatus
      params.carNoList = this.$toggleSplitJoin(params, 'carNoList') || []
      this.getCustomerIds(params, false)
      this.searchParams = _.cloneDeep(params)
      return params
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@/views/common/common/styles/table.scss'
</style>
