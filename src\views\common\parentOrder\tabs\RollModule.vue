<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <div class="dst-table-content">
      <sys-grid
        local-key="common_roll_orders_list"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setOffsetBalanceListConfig"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
/* eslint-disable indent */
import mixin from '@/mixins/table.js'
import { setOffsetBalanceListConfig } from './functions/table'
import { getParentOrderAdjustOrderList } from '@/api/common/rental/parent'
import orderMixins from '@/views/common/common/mixins/orderLink'
export default {
  name: 'RollModule',
  mixins: [mixin, orderMixins],
  props: {
    // 当前父订单code
    parentCode: {
      type: String,
      default: '',
      required: true,
    },
    // 当前父订单id
    parentId: {
      type: String,
      default: '',
      required: true,
    },
    // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
    type: {
      type: [Number, String],
      default: 0,
      required: true,
    },
  },
  data() {
    return {
      // loading加载
      isLock: false,
      // 子账单信息列表数据
      tableList: [],
      // 轧差订单总数
      paginationTotal: 0,
    }
  },
  computed: {
    // 轧差订单列表配置
    setOffsetBalanceListConfig,
  },
  methods: {
    /**
     * @description:获取轧差订单列表信息
     */
    async getTableData() {
      await this.$nextTick(async () => {
        if (this.parentCode) {
          this.isLock = true
          const data = this.getParams()
          await getParentOrderAdjustOrderList(
            {
              parentOrderCode: this.parentCode,
              id: this.parentId,
              ...data,
            },
            this.type
          )
            .then((res) => {
              this.tableList = res.data.list || []
              this.paginationTotal = res.data.totalNum || 0
            })
            .finally(() => {
              this.isLock = false
            })
        }
      })
    },
    /**
     * @description:来自父订单的刷新
     */
    refresh() {
      this.getTableData()
    },
    /**
     * @description:格式化列表请求参数
     */
    getParams() {
      this.searchParams = {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...this.searchData,
      }
      return this.searchParams
    },
  },
}
</script>
